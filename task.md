1. 目前的数据格式太繁琐了, 存储的数据类型每一条数据的格式如下 :

```json
{
  id : 1, 
  diciType : DICE_TYPES.SANHUA , 
  dice : {
  	0: 4 , 
    1: 5 , 
    2: 2
	}, 
  beerType : BeverageType.YUAN_JIANG, 
  weight : 1 ,
  revenue : 10 , 
  cost : 3.75 , 
  profit : 6.25
}
```

2. 生成骰子点数的逻辑需要整改

   ```java
   let totalRevenue = 0 ;  // 总收入
   let totalWeight = 0 ; // 总重量
   let totalCost = 0 ; // 总成本
   let totalProfit = 0 ; // 总利润
   let profitMargin = 0 ; // 毛利率
   let dice = []; // 骰子数组
   history.forEach(record => {
     totalRevenue +=record.revenue || 0;
     totalWeight += record.weight || 0 ; 
     totalCost += record.cost || 0 ; 
     totalProfit += record.profit || 0 ; 
   })
   profitMargin = (totalRevenue - totalCost) / totalRevenue 
   if(profitMargin>40){
     dice = generateDice(); // 直接生成随机点数, 
   }else{
     // todo : 在毛利率小于 40 时, 需要通过算法实现生成散花, 从而保证毛利率恢复在 40 以上。
   }  
   ```

3. 上述两部分内容涉及到了整个项目, 请从 index.vue 里面的 最下方的两个啤酒按钮开始, 以上述逻辑进行项目的重构。不需要的代码或组件进行删除处理。