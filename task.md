1. 目前所有使用格式都需要统一处理, 有枚举了, index.vue , 弹窗, 工具类里面, 都应该统一用枚举, 而不应该多样性, 请对代码进行重构及优化。
2. 算法要充分考虑到是点击了原浆, 还是果啤。
3. 记录分析面板里面, 如果没有数据, 直接都显示 0 就可以了, 不需要再添加 no-data 的相关样式。
4. 显示数据应该显示两位小数, 所有变量,枚举,函数, 都应该从工具类中来实现。
5. 不要有什么兼容方式, 保证只有一种有效的利用及调用等等。
6. 对代码进行优化。项目中所有不需要的代码, 样式, 都需要进行删除处理。
7. AnalysisModal.vue 里面添加一个监听, 在面板打开时, 实时获取到 localstorage 里面的真实数据, 再进行展示。这需要考虑diceCalculator.js 工具类里面的 getAnalysisData 函数, 是否是真正的获取到了 localstorage 里面的真实数据。我目前的问题是, 不管清除记录还是添加记录, 点击记录分析, 记录分析面板展开后, 都不是即时的数据。
8. 记录分析里面的每一行数据, 都需要在diceCalculator.js 工具类里面创建一个函数, 并返回对应的值, 不要定义一个函数来实现, 耦合太严重了。不方便对算法进行修改。