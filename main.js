/*
 * @Author: resty <EMAIL>
 * @Date: 2024-07-08 18:51:58
 * @LastEditors: resty <EMAIL>
 * @LastEditTime: 2025-08-11 16:42:37
 * @FilePath: /top.resty.dice_game/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// #ifndef VUE3
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
import 'animate.css'

export function createApp() {
  const app = createSSRApp(App)
  
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err, info)
  }
  
  // 全局警告处理
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('Vue Warning:', msg, trace)
  }
  
  return {
    app
  }
}
// #endif