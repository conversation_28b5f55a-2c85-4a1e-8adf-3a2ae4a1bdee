/**
 * 点数计算器 - 骰子游戏与啤酒销售管理
 */

// 啤酒类型枚举 - 统一使用英文常量
const BeverageType = Object.freeze({
  YUAN_JIANG: 'YUAN_JIANG',  // 原浆
  GUO_PI: 'GUO_PI',          // 果啤

  // 枚举工具方法
  isValid(type) {
    return Object.values(this).includes(type);
  },

  getAll() {
    return [this.YUAN_JIANG, this.GUO_PI];
  },

  getName(type) {
    const names = {
      [this.YUAN_JIANG]: '原浆',
      [this.GUO_PI]: '果啤'
    };
    return names[type] || '未知';
  }
});

// 啤酒配置 - 根据实际业务逻辑调整
const BEER_CONFIG = {
  // 游戏费用 (元/次)
  gameFee: {
    [BeverageType.YUAN_JIANG]: 10,    // 原浆 10元/次
    [BeverageType.GUO_PI]: 15         // 果啤 15元/次
  },
  // 成本 (元/斤) - 根据您提供的实际成本
  costs: {
    [BeverageType.YUAN_JIANG]: 3.75,  // 原浆成本 3.75元/斤
    [BeverageType.GUO_PI]: 6.0        // 果啤成本 6元/斤
  },
  // 中文名称
  names: {
    [BeverageType.YUAN_JIANG]: '原浆',
    [BeverageType.GUO_PI]: '果啤'
  }
};

// 点数类型枚举配置
const DICE_TYPES = Object.freeze({
  SANHUA: { name: '散花', weight: 1 },    // 三个不同点数
  DUIZI: { name: '对子', weight: 2 },     // 两个相同点数
  SHUNZI: { name: '顺子', weight: 3 },    // 连续三个点数
  BAOZI: { name: '豹子', weight: 4 },     // 三个相同点数

  // 枚举工具方法
  getAll() {
    return [this.SANHUA, this.DUIZI, this.SHUNZI, this.BAOZI];
  },

  getByName(name) {
    return this.getAll().find(type => type.name === name);
  },

  getWeight(name) {
    const type = this.getByName(name);
    return type ? type.weight : 1;
  }
});

class DiceCalculator {
  constructor() {
    this.history = this.loadHistory();
    this.cache = new Map(); // 添加缓存机制
    this.maxHistorySize = 10000; // 最大历史记录数量
  }

  /**
   * 生成三个随机点数 (1-6)
   */
  generateDice() {
    return [
      Math.floor(Math.random() * 6) + 1,
      Math.floor(Math.random() * 6) + 1,
      Math.floor(Math.random() * 6) + 1
    ];
  }

  /**
   * 判断点数类型
   */
  getDiceType(dice) {
    const sorted = [...dice].sort((a, b) => a - b);
    const [a, b, c] = sorted;

    // 豹子：三个相同
    if (a === b && b === c) {
      return DICE_TYPES.BAOZI;
    }

    // 顺子：连续三个数
    if (b === a + 1 && c === b + 1) {
      return DICE_TYPES.SHUNZI;
    }

    // 对子：两个相同
    if (a === b || b === c || a === c) {
      return DICE_TYPES.DUIZI;
    }

    // 散花：三个不同
    return DICE_TYPES.SANHUA;
  }

  /**
   * 计算啤酒重量
   */
  calculateBeerWeight(diceType) {
    return diceType.weight;
  }

  /**
   * 计算毛利率
   */
  calculateProfitMargin(revenue, cost) {
    if (revenue === 0) return 0;
    return ((revenue - cost) / revenue) * 100;
  }

  /**
   * 检查毛利率是否符合要求
   */
  isProfitMarginAcceptable(revenue, cost, minMargin = 40) {
    const margin = this.calculateProfitMargin(revenue, cost);
    return margin >= minMargin;
  }

  /**
   * 根据毛利率要求调整成本配置
   */
  adjustCostForProfitMargin(beerType, targetMargin = 40) {
    const gameFee = BEER_CONFIG.gameFee[beerType];
    const maxCostPerJin = gameFee * (1 - targetMargin / 100);
    return Math.max(maxCostPerJin, 1); // 确保成本不低于1元
  }

  /**
   * 获取毛利率统计信息
   */
  getProfitMarginStats() {
    const stats = this.calculateTotalStats();
    const marginRanges = {
      excellent: 0, // >= 60%
      good: 0,      // 40-59%
      warning: 0,   // 20-39%
      poor: 0       // < 20%
    };

    this.history.forEach(record => {
      const revenue = record.游戏费用 || record.gameFee || 0;
      const cost = record.成本 || record.totalCost || 0;
      const margin = this.calculateProfitMargin(revenue, cost);

      if (margin >= 60) marginRanges.excellent++;
      else if (margin >= 40) marginRanges.good++;
      else if (margin >= 20) marginRanges.warning++;
      else marginRanges.poor++;
    });

    return {
      ranges: marginRanges,
      overall: stats.totalRevenue > 0 ? this.calculateProfitMargin(stats.totalRevenue, stats.totalCost) : 0,
      average: stats.totalGames > 0 ? (stats.totalProfit / stats.totalRevenue * 100) : 0
    };
  }

  /**
   * 生成符合毛利率要求的骰子点数
   */
  generateDiceWithProfitControl(beerType = BeverageType.YUAN_JIANG) {
    const gameFee = BEER_CONFIG.gameFee[beerType];
    const costPerJin = BEER_CONFIG.costs[beerType];
    const minProfitMargin = 40; // 最低毛利率40%
    const maxAttempts = 100; // 最大尝试次数
    // 定义变量
    // dice 骰子点数
    // diceType 骰子类型
    // weight 啤酒重量
    // totalCost 总成本
    // profit 盈利
    // profitMargin 毛利率
    // revenue 收入
    // totalRevenue 总收入
    let dice, diceType, weight, totalCost, profit, profitMargin, revenue , totalRevenue;
    // 1. 遍历历史记录, 获取总收入,
    history.forEach(record => {
      totalRevenue +=record.revenue || 0;
    })


    let attempts = 0;


    do {
      dice = this.generateDice();
      diceType = this.getDiceType(dice);
      weight = this.calculateBeerWeight(diceType);

      // 正确的游戏逻辑：
      // 客户花10元玩一次，摇出对子得到2斤啤酒
      // 总收入 = 10元（客户支付的费用）
      // 总成本 = 2斤 × 3.75元/斤 = 7.5元（提供啤酒的成本）
      // 盈利 = 10元 - 7.5元 = 2.5元
      // 毛利率 = (10元 - 7.5元) / 10元 × 100% = 25%
      revenue = gameFee; // 收入 = 客户支付的游戏费用
      totalCost = weight * costPerJin; // 成本 = 获得的啤酒重量 × 每斤成本
      profit = revenue - totalCost; // 盈利 = 收入 - 成本
      profitMargin = this.calculateProfitMargin(revenue, totalCost);

      attempts++;

      // 如果毛利率达到要求或尝试次数过多，跳出循环
      if (profitMargin >= minProfitMargin || attempts >= maxAttempts) {
        break;
      }
    } while (true);

    return {
      dice,
      diceType,
      weight,
      revenue,
      totalCost,
      profit,
      profitMargin,
      attempts
    };
  }

  /**
   * 计算单次游戏结果（带毛利率控制）
   */
  calculateGameResult(beerType = BeverageType.YUAN_JIANG) {
    const gameFee = BEER_CONFIG.gameFee[beerType];
    const costPerJin = BEER_CONFIG.costs[beerType];

    // 生成符合毛利率要求的结果
    const gameData = this.generateDiceWithProfitControl(beerType);

    const result = {
      dice: gameData.dice,
      diceType: gameData.diceType.name,
      weight: gameData.weight,
      beerType,
      beerTypeName: BEER_CONFIG.names[beerType],
      gameFee,
      costPerJin,
      revenue: gameData.revenue,
      totalCost: gameData.totalCost,
      profit: gameData.profit,
      profitMargin: Math.round(gameData.profitMargin * 100) / 100, // 保留2位小数
      attempts: gameData.attempts,
      timestamp: new Date().toISOString()
    };

    // 添加到历史记录
    this.addToHistory(result);

    return result;
  }

  /**
   * 添加到历史记录（按照指定格式）
   */
  addToHistory(result) {
    try {
      // 按照task.md指定的格式存储
      const record = {
        记录序列: this.history.length + 1,
        啤酒类型: result.beerTypeName || BEER_CONFIG.names[result.beerType],
        骰子点数: result.dice,
        点数类型: result.diceType,
        重量: result.weight,
        游戏费用: result.gameFee,
        成本: result.totalCost,
        盈利: result.profit,
        毛利率: result.profitMargin,
        时间戳: result.timestamp,
        // 保留原始数据用于兼容性
        _raw: result
      };

      this.history.push(record);

      // 限制历史记录大小
      this.limitHistorySize();

      // 清除缓存
      this.clearCache();

      this.saveHistory();
    } catch (error) {
      console.error('添加历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取历史记录
   */
  getHistory() {
    return this.history;
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = [];
    this.saveHistory();
  }

  /**
   * 计算总体统计（直接使用存储的数据，确保一致性）
   */
  calculateTotalStats(history = null) {
    const data = history || this.history;

    if (data.length === 0) {
      return {
        totalGames: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        totalWeight: 0,
        averageProfit: 0,
        averageProfitPerJin: 0,
        diceTypeStats: {
          '散花': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          '对子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          '顺子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          '豹子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
        },
        beerTypeStats: {
          '原浆': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          '果啤': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
        }
      };
    }

    // 初始化统计数据
    const stats = {
      totalGames: data.length,
      totalRevenue: 0,
      totalCost: 0,
      totalProfit: 0,
      totalWeight: 0,
      diceTypeStats: {
        '散花': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
        '对子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
        '顺子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
        '豹子': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
      },
      beerTypeStats: {
        '原浆': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
        '果啤': { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
      }
    };

    // 遍历所有记录进行统计
    data.forEach(record => {
      // 获取记录数据（兼容新旧格式）
      const beerType = record.啤酒类型 || record.beerTypeName || '原浆';
      const diceType = record.点数类型 || record.diceType || '散花';
      const weight = record.重量 || record.weight || 0;

      // 直接使用存储的数据，确保一致性
      const revenue = record.游戏费用 || record.gameFee || record.revenue || 0;
      const cost = record.成本 || record.totalCost || 0;
      const profit = record.盈利 || record.profit || 0;

      // 累计总体统计
      stats.totalRevenue += revenue;
      stats.totalCost += cost;
      stats.totalProfit += profit;
      stats.totalWeight += weight;

      // 点数类型统计
      if (stats.diceTypeStats[diceType]) {
        stats.diceTypeStats[diceType].count++;
        stats.diceTypeStats[diceType].totalRevenue += revenue;
        stats.diceTypeStats[diceType].totalCost += cost;
        stats.diceTypeStats[diceType].totalProfit += profit;
        stats.diceTypeStats[diceType].totalWeight += weight;
      }

      // 啤酒类型统计
      if (stats.beerTypeStats[beerType]) {
        stats.beerTypeStats[beerType].count++;
        stats.beerTypeStats[beerType].totalRevenue += revenue;
        stats.beerTypeStats[beerType].totalCost += cost;
        stats.beerTypeStats[beerType].totalProfit += profit;
        stats.beerTypeStats[beerType].totalWeight += weight;
      }
    });

    // 计算平均值
    stats.averageProfit = stats.totalGames > 0 ? stats.totalProfit / stats.totalGames : 0;
    stats.averageProfitPerJin = stats.totalWeight > 0 ? stats.totalProfit / stats.totalWeight : 0;

    return stats;
  }

  /**
   * 为AnalysisModal提供专门的统计数据（按照正确的业务逻辑）
   */
  getAnalysisData() {
    const stats = this.calculateTotalStats();

    return {
      // 第一行：总体统计
      overall: {
        totalRevenue: stats.totalRevenue,
        totalCost: stats.totalCost,
        totalProfit: stats.totalProfit,
        totalMargin: stats.totalRevenue > 0 ? ((stats.totalProfit / stats.totalRevenue) * 100) : 0
      },

      // 第二行：啤酒类型统计
      beer: {
        draft: {
          revenue: stats.beerTypeStats['原浆'].totalRevenue,
          cost: stats.beerTypeStats['原浆'].totalCost,
          profit: stats.beerTypeStats['原浆'].totalProfit,
          margin: stats.beerTypeStats['原浆'].totalRevenue > 0 ?
            ((stats.beerTypeStats['原浆'].totalProfit / stats.beerTypeStats['原浆'].totalRevenue) * 100) : 0
        },
        fruit: {
          revenue: stats.beerTypeStats['果啤'].totalRevenue,
          cost: stats.beerTypeStats['果啤'].totalCost,
          profit: stats.beerTypeStats['果啤'].totalProfit,
          margin: stats.beerTypeStats['果啤'].totalRevenue > 0 ?
            ((stats.beerTypeStats['果啤'].totalProfit / stats.beerTypeStats['果啤'].totalRevenue) * 100) : 0
        }
      },

      // 第三行：点数类型统计
      dice: {
        散花: {
          revenue: stats.diceTypeStats['散花'].totalRevenue,
          cost: stats.diceTypeStats['散花'].totalCost,
          profit: stats.diceTypeStats['散花'].totalProfit,
          margin: stats.diceTypeStats['散花'].totalRevenue > 0 ?
            ((stats.diceTypeStats['散花'].totalProfit / stats.diceTypeStats['散花'].totalRevenue) * 100) : 0
        },
        对子: {
          revenue: stats.diceTypeStats['对子'].totalRevenue,
          cost: stats.diceTypeStats['对子'].totalCost,
          profit: stats.diceTypeStats['对子'].totalProfit,
          margin: stats.diceTypeStats['对子'].totalRevenue > 0 ?
            ((stats.diceTypeStats['对子'].totalProfit / stats.diceTypeStats['对子'].totalRevenue) * 100) : 0
        },
        顺子: {
          revenue: stats.diceTypeStats['顺子'].totalRevenue,
          cost: stats.diceTypeStats['顺子'].totalCost,
          profit: stats.diceTypeStats['顺子'].totalProfit,
          margin: stats.diceTypeStats['顺子'].totalRevenue > 0 ?
            ((stats.diceTypeStats['顺子'].totalProfit / stats.diceTypeStats['顺子'].totalRevenue) * 100) : 0
        },
        豹子: {
          revenue: stats.diceTypeStats['豹子'].totalRevenue,
          cost: stats.diceTypeStats['豹子'].totalCost,
          profit: stats.diceTypeStats['豹子'].totalProfit,
          margin: stats.diceTypeStats['豹子'].totalRevenue > 0 ?
            ((stats.diceTypeStats['豹子'].totalProfit / stats.diceTypeStats['豹子'].totalRevenue) * 100) : 0
        }
      },

      // 第四行：补充统计
      supplement: {
        totalGames: stats.totalGames,           // 游戏总次数 = 记录集合的长度
        totalWeight: stats.totalWeight,         // 总斤数 = 根据所有记录的点数算出的总斤数
        averageProfit: stats.averageProfit,     // 平均每次盈利 = 总盈利 / 次数
        averageProfitPerJin: stats.averageProfitPerJin  // 平均每斤盈利 = 总盈利 / 总斤数
      },

      // 原始统计数据
      raw: stats
    };
  }

  /**
   * 保存历史记录到本地存储
   */
  saveHistory() {
    if (typeof localStorage !== 'undefined') {
      // 使用新的localStorage键名
      localStorage.setItem('dice_records', JSON.stringify(this.history));
    }
  }

  /**
   * 从本地存储加载历史记录
   */
  loadHistory() {
    if (typeof localStorage !== 'undefined') {
      // 先尝试新格式 dice_records
      let saved = localStorage.getItem('dice_records');
      if (saved) {
        return JSON.parse(saved);
      }
    }
    return [];
  }

  /**
   * 迁移旧数据格式
   */
  migrateOldData(oldData) {
    this.history = oldData.map((item, index) => ({
      记录序列: index + 1,
      啤酒类型: item.beerTypeName || BEER_CONFIG.names[item.beerType] || '原浆',
      骰子点数: item.dice || [1, 1, 1],
      点数类型: item.diceType || '散花',
      重量: item.weight || 1,
      游戏费用: item.gameFee || 10,
      成本: item.totalCost || 0,
      盈利: item.profit || 0,
      毛利率: item.profitMargin || 0,
      时间戳: item.timestamp || new Date().toISOString(),
      _raw: item
    }));
    this.saveHistory();
  }

  /**
   * 导出历史记录为 JSON
   */
  exportHistory() {
    return JSON.stringify(this.history, null, 2);
  }

  /**
   * 导入历史记录
   */
  importHistory(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      if (Array.isArray(data)) {
        this.history = data;
        this.saveHistory();
        return true;
      }
    } catch (error) {
      console.error('导入历史记录失败:', error);
    }
    return false;
  }


  /**
   * 格式化显示数据的工具方法
   */
  formatDisplayData(record) {
    return {
      序列: record.记录序列 || 'N/A',
      啤酒: record.啤酒类型 || 'N/A',
      点数: Array.isArray(record.骰子点数) ? record.骰子点数.join('-') : 'N/A',
      类型: record.点数类型 || 'N/A',
      重量: `${record.重量 || 0}斤`,
      费用: `${record.游戏费用 || 0}元`,
      成本: `${record.成本 || 0}元`,
      盈利: `${record.盈利 || 0}元`,
      毛利率: `${record.毛利率 || 0}%`
    };
  }

  /**
   * 验证数据完整性
   */
  validateRecord(record) {
    const required = ['记录序列', '啤酒类型', '骰子点数', '点数类型', '重量'];
    const missing = required.filter(field => !record.hasOwnProperty(field));

    return {
      isValid: missing.length === 0,
      missingFields: missing,
      record: record
    };
  }

  /**
   * 获取游戏配置信息
   */
  getGameConfig() {
    return {
      beerTypes: BeverageType,
      diceTypes: DICE_TYPES,
      beerConfig: BEER_CONFIG,
      minProfitMargin: 40
    };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存的统计数据
   */
  getCachedStats() {
    const cacheKey = `stats_${this.history.length}_${Date.now()}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const stats = this.calculateTotalStats();
    this.cache.set(cacheKey, stats);

    // 限制缓存大小
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    return stats;
  }

  /**
   * 性能监控
   */
  performanceMonitor(operation, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    if (end - start > 100) { // 如果操作超过100ms
      console.warn(`性能警告: ${operation} 耗时 ${(end - start).toFixed(2)}ms`);
    }

    return result;
  }

  /**
   * 数据完整性检查
   */
  validateDataIntegrity() {
    const issues = [];

    this.history.forEach((record, index) => {
      const validation = this.validateRecord(record);
      if (!validation.isValid) {
        issues.push({
          index,
          record,
          issues: validation.missingFields
        });
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
      totalRecords: this.history.length
    };
  }

  /**
   * 自动清理过期数据
   */
  cleanupOldData(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
    const cutoffTime = new Date(Date.now() - maxAge);
    const originalLength = this.history.length;

    this.history = this.history.filter(record => {
      const recordTime = new Date(record.时间戳 || record.timestamp);
      return recordTime > cutoffTime;
    });

    if (this.history.length !== originalLength) {
      this.saveHistory();
      this.clearCache();
      console.log(`清理了 ${originalLength - this.history.length} 条过期记录`);
    }
  }

  /**
   * 限制历史记录大小
   */
  limitHistorySize() {
    if (this.history.length > this.maxHistorySize) {
      const excess = this.history.length - this.maxHistorySize;
      this.history = this.history.slice(excess);
      this.saveHistory();
      this.clearCache();
      console.log(`清理了 ${excess} 条最旧的记录`);
    }
  }

  /**
   * 重置所有数据
   */
  reset() {
    this.clearHistory();
    this.clearCache();
    console.log('DiceCalculator已重置');
  }

  /**
   * 获取系统状态
   */
  getSystemStatus() {
    return {
      historyCount: this.history.length,
      cacheSize: this.cache.size,
      maxHistorySize: this.maxHistorySize,
      memoryUsage: this.history.length * 200, // 估算内存使用（字节）
      lastUpdate: this.history.length > 0 ? this.history[this.history.length - 1].时间戳 : null
    };
  }
}

// 创建calculateStats函数供外部使用
function calculateStats() {
  const calculator = new DiceCalculator();
  return calculator.calculateTotalStats();
}

// 创建单例实例
const diceCalculator = new DiceCalculator();

// 添加调试方法
diceCalculator.debugTest = function() {
  console.log('=== DiceCalculator 调试测试 ===');
  console.log('历史记录数量:', this.history.length);
  console.log('历史记录:', this.history);

  const stats = this.calculateTotalStats();
  console.log('统计数据:', stats);

  const analysisData = this.getAnalysisData();
  console.log('分析数据:', analysisData);

  return {
    historyCount: this.history.length,
    stats,
    analysisData
  };
};

// 格式化工具函数
const formatNumber = (value) => {
  const num = Number(value);
  if (!isFinite(num)) return '0';

  // 如果是整数，不显示小数
  if (Number.isInteger(num)) {
    return num.toString();
  }

  // 否则显示两位小数
  return num.toFixed(2);
};

const formatCurrency = (value) => {
  const num = Number(value);
  if (!isFinite(num)) return '0.00';
  return num.toFixed(2);
};

const formatPercentage = (value) => {
  const num = Number(value);
  if (!isFinite(num)) return '0.00';
  return num.toFixed(2);
};

const formatInteger = (value) => {
  const num = Number(value);
  if (!isFinite(num)) return '0';
  return Math.round(num).toString();
};

// 独立的分析指标计算函数
const calcTotalRevenue = () => {
  const history = diceCalculator.loadHistory();
  return history.reduce((sum, record) => {
    return sum + (record.游戏费用 || record.gameFee || record.revenue || 0);
  }, 0);
};

const calcTotalCost = () => {
  const history = diceCalculator.loadHistory();
  return history.reduce((sum, record) => {
    return sum + (record.成本 || record.totalCost || 0);
  }, 0);
};

const calcTotalProfit = () => {
  const history = diceCalculator.loadHistory();
  return history.reduce((sum, record) => {
    return sum + (record.盈利 || record.profit || 0);
  }, 0);
};

const calcTotalMargin = () => {
  const revenue = calcTotalRevenue();
  const cost = calcTotalCost();
  if (revenue === 0) return 0;
  return ((revenue - cost) / revenue) * 100;
};

const calcBeerTypeRevenue = (beerType) => {
  const history = diceCalculator.loadHistory();
  const typeName = BeverageType.getName(beerType);
  return history
    .filter(record => (record.啤酒类型 || record.beerTypeName) === typeName)
    .reduce((sum, record) => sum + (record.游戏费用 || record.gameFee || record.revenue || 0), 0);
};

const calcBeerTypeCost = (beerType) => {
  const history = diceCalculator.loadHistory();
  const typeName = BeverageType.getName(beerType);
  return history
    .filter(record => (record.啤酒类型 || record.beerTypeName) === typeName)
    .reduce((sum, record) => sum + (record.成本 || record.totalCost || 0), 0);
};

const calcBeerTypeProfit = (beerType) => {
  const history = diceCalculator.loadHistory();
  const typeName = BeverageType.getName(beerType);
  return history
    .filter(record => (record.啤酒类型 || record.beerTypeName) === typeName)
    .reduce((sum, record) => sum + (record.盈利 || record.profit || 0), 0);
};

const calcBeerTypeMargin = (beerType) => {
  const revenue = calcBeerTypeRevenue(beerType);
  const cost = calcBeerTypeCost(beerType);
  if (revenue === 0) return 0;
  return ((revenue - cost) / revenue) * 100;
};

const calcDiceTypeRevenue = (diceType) => {
  const history = diceCalculator.loadHistory();
  return history
    .filter(record => (record.点数类型 || record.diceType) === diceType)
    .reduce((sum, record) => sum + (record.游戏费用 || record.gameFee || record.revenue || 0), 0);
};

const calcDiceTypeCost = (diceType) => {
  const history = diceCalculator.loadHistory();
  return history
    .filter(record => (record.点数类型 || record.diceType) === diceType)
    .reduce((sum, record) => sum + (record.成本 || record.totalCost || 0), 0);
};

const calcDiceTypeProfit = (diceType) => {
  const history = diceCalculator.loadHistory();
  return history
    .filter(record => (record.点数类型 || record.diceType) === diceType)
    .reduce((sum, record) => sum + (record.盈利 || record.profit || 0), 0);
};

const calcDiceTypeMargin = (diceType) => {
  const revenue = calcDiceTypeRevenue(diceType);
  const cost = calcDiceTypeCost(diceType);
  if (revenue === 0) return 0;
  return ((revenue - cost) / revenue) * 100;
};

const calcTotalGames = () => {
  const history = diceCalculator.loadHistory();
  return history.length;
};

const calcTotalWeight = () => {
  const history = diceCalculator.loadHistory();
  return history.reduce((sum, record) => {
    return sum + (record.重量 || record.weight || 0);
  }, 0);
};

const calcAverageProfit = () => {
  const totalProfit = calcTotalProfit();
  const totalGames = calcTotalGames();
  return totalGames > 0 ? totalProfit / totalGames : 0;
};

const calcAverageProfitPerJin = () => {
  const totalProfit = calcTotalProfit();
  const totalWeight = calcTotalWeight();
  return totalWeight > 0 ? totalProfit / totalWeight : 0;
};

export default diceCalculator;
export {
  BEER_CONFIG,
  DICE_TYPES,
  BeverageType,
  DiceCalculator,
  calculateStats,
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatInteger,
  calcTotalRevenue,
  calcTotalCost,
  calcTotalProfit,
  calcTotalMargin,
  calcBeerTypeRevenue,
  calcBeerTypeCost,
  calcBeerTypeProfit,
  calcBeerTypeMargin,
  calcDiceTypeRevenue,
  calcDiceTypeCost,
  calcDiceTypeProfit,
  calcDiceTypeMargin,
  calcTotalGames,
  calcTotalWeight,
  calcAverageProfit,
  calcAverageProfitPerJin
};