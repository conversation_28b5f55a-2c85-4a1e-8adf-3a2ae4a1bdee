/**
 * 点数计算器 - 骰子游戏与啤酒销售管理
 * 重构版本 - 简化数据格式
 */

// 啤酒类型枚举
const BeverageType = Object.freeze({
  YUAN_JIANG: 'YUAN_JIANG',  // 原浆
  GUO_PI: 'GUO_PI',          // 果啤
});

// 点数类型枚举
const DICE_TYPES = Object.freeze({
  SANHUA: 'SANHUA',    // 散花 - 三个不同点数
  DUIZI: 'DUIZI',      // 对子 - 两个相同点数
  SHUNZI: 'SHUNZI',    // 顺子 - 连续三个点数
  BAOZI: 'BAOZI',      // 豹子 - 三个相同点数
});

// 啤酒配置
const BEER_CONFIG = {
  // 游戏费用 (元/次)
  gameFee: {
    [BeverageType.YUAN_JIANG]: 10,    // 原浆 10元/次
    [BeverageType.GUO_PI]: 15         // 果啤 15元/次
  },
  // 成本 (元/斤)
  costs: {
    [BeverageType.YUAN_JIANG]: 3.75,  // 原浆成本 3.75元/斤
    [BeverageType.GUO_PI]: 6.0        // 果啤成本 6元/斤
  }
};

// 点数类型对应的重量
const DICE_WEIGHTS = {
  [DICE_TYPES.SANHUA]: 1,    // 散花 1斤
  [DICE_TYPES.DUIZI]: 2,     // 对子 2斤
  [DICE_TYPES.SHUNZI]: 3,    // 顺子 3斤
  [DICE_TYPES.BAOZI]: 4,     // 豹子 4斤
};

// 点数类型对应的中文名称
const DICE_TYPE_NAMES = {
  [DICE_TYPES.SANHUA]: '散花',
  [DICE_TYPES.DUIZI]: '对子',
  [DICE_TYPES.SHUNZI]: '顺子',
  [DICE_TYPES.BAOZI]: '豹子',
};

// 啤酒类型对应的中文名称
const BEER_TYPE_NAMES = {
  [BeverageType.YUAN_JIANG]: '原浆',
  [BeverageType.GUO_PI]: '果啤',
};

class DiceCalculator {
  constructor() {
    this.history = this.loadHistory();
  }

  /**
   * 生成三个随机点数 (1-6)
   */
  generateDice() {
    return [
      Math.floor(Math.random() * 6) + 1,
      Math.floor(Math.random() * 6) + 1,
      Math.floor(Math.random() * 6) + 1
    ];
  }

  /**
   * 生成散花类型的骰子点数
   */
  generateSanhuaDice() {
    let dice;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      dice = this.generateDice();
      attempts++;
    } while (this.getDiceType(dice) !== DICE_TYPES.SANHUA && attempts < maxAttempts);

    // 如果无法生成散花，手动构造一个
    if (this.getDiceType(dice) !== DICE_TYPES.SANHUA) {
      dice = [1, 5, 3]; // 确保是散花
    }

    return dice;
  }

  /**
   * 判断点数类型
   */
  getDiceType(dice) {
    const sorted = [...dice].sort((a, b) => a - b);
    const [a, b, c] = sorted;

    // 豹子：三个相同
    if (a === b && b === c) {
      return DICE_TYPES.BAOZI;
    }

    // 顺子：连续三个数
    if (b === a + 1 && c === b + 1) {
      return DICE_TYPES.SHUNZI;
    }

    // 对子：两个相同
    if (a === b || b === c || a === c) {
      return DICE_TYPES.DUIZI;
    }

    // 散花：三个不同
    return DICE_TYPES.SANHUA;
  }

  /**
   * 计算啤酒重量
   */
  calculateBeerWeight(diceType) {
    return DICE_WEIGHTS[diceType] || 1;
  }

  /**
   * 计算历史记录的总体数据
   */
  calculateTotalStats() {
    let totalRevenue = 0;
    let totalWeight = 0;
    let totalCost = 0;
    let totalProfit = 0;

    this.history.forEach(record => {
      totalRevenue += record.revenue || 0;
      totalWeight += record.weight || 0;
      totalCost += record.cost || 0;
      totalProfit += record.profit || 0;
    });

    return {
      totalRevenue,
      totalWeight,
      totalCost,
      totalProfit
    };
  }

  /**
   * 计算毛利率
   */
  calculateProfitMargin(totalRevenue, totalCost) {
    if (totalRevenue === 0) return 0;
    return ((totalRevenue - totalCost) / totalRevenue) * 100;
  }

  /**
   * 按照task.md要求实现毛利率控制的骰子生成逻辑
   */
  generateDiceWithProfitControl(beerType = BeverageType.YUAN_JIANG) {
    // 1. 计算历史记录的总体数据
    const stats = this.calculateTotalStats();
    console.log(stats)
    let totalRevenue = stats.totalRevenue;
    let totalWeight = stats.totalWeight;
    let totalCost = stats.totalCost;
    let totalProfit = stats.totalProfit;

    // 2. 计算当前毛利率
    let profitMargin = this.calculateProfitMargin(totalRevenue, totalCost);

    let dice = [];

    // 3. 按照task.md的逻辑：如果毛利率>40%，直接生成随机点数
    if (profitMargin > 40) {
      dice = this.generateDice();
    } else {
      // 4. 如果毛利率<=40%，生成散花确保毛利率恢复到40%以上
      dice = this.generateSanhuaDice();
    }

    // 5. 计算本次游戏的结果
    const diceType = this.getDiceType(dice);
    const weight = this.calculateBeerWeight(diceType);
    const revenue = BEER_CONFIG.gameFee[beerType];
    const cost = weight * BEER_CONFIG.costs[beerType];
    const profit = revenue - cost;

    return {
      dice,
      diceType,
      weight,
      revenue,
      cost,
      profit
    };
  }

  /**
   * 计算单次游戏结果（按照task.md简化的数据格式）
   */
  calculateGameResult(beerType = BeverageType.YUAN_JIANG) {
    // 生成符合毛利率要求的结果
    const gameData = this.generateDiceWithProfitControl(beerType);

    // 按照task.md要求的简化数据格式
    const result = {
      id: this.history.length + 1,
      diceType: gameData.diceType,
      dice: {
        0: gameData.dice[0],
        1: gameData.dice[1],
        2: gameData.dice[2]
      },
      beerType: beerType,
      weight: gameData.weight,
      revenue: gameData.revenue,
      cost: gameData.cost,
      profit: gameData.profit
    };

    // 添加到历史记录
    this.addToHistory(result);

    return result;
  }

  /**
   * 添加到历史记录（按照task.md简化格式）
   */
  addToHistory(result) {
    this.history.push(result);
    this.saveHistory();
  }

  /**
   * 获取历史记录
   */
  getHistory() {
    return this.history;
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = [];
    this.saveHistory();
  }

  /**
   * 保存历史记录到本地存储
   */
  saveHistory() {
    if (typeof uni !== 'undefined' && uni.setStorageSync) {
      // uni-app环境
      uni.setStorageSync('dice_records', JSON.stringify(this.history));
    } else if (typeof localStorage !== 'undefined') {
      // 浏览器环境
      localStorage.setItem('dice_records', JSON.stringify(this.history));
    }
  }

  /**
   * 从本地存储加载历史记录
   */
  loadHistory() {
    try {
      let saved = null;
      if (typeof uni !== 'undefined' && uni.getStorageSync) {
        // uni-app环境
        saved = uni.getStorageSync('dice_records');
      } else if (typeof localStorage !== 'undefined') {
        // 浏览器环境
        saved = localStorage.getItem('dice_records');
      }

      if (saved) {
        return JSON.parse(saved);
      }
    } catch (e) {
      console.error('加载历史记录失败:', e);
    }
    return [];
  }



}

// 创建单例实例
const diceCalculator = new DiceCalculator();

export default diceCalculator;
export {
  BeverageType,
  DICE_TYPES,
  DICE_WEIGHTS,
  DICE_TYPE_NAMES,
  BEER_TYPE_NAMES,
  BEER_CONFIG
};