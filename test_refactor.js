/**
 * 测试重构后的骰子游戏功能
 */

// 模拟 uni-app 环境
global.uni = {
  setStorageSync: (key, value) => {
    console.log(`保存到存储: ${key} = ${value}`);
  },
  getStorageSync: (key) => {
    console.log(`从存储读取: ${key}`);
    return null; // 模拟空存储
  }
};

// 导入重构后的模块
const diceCalculator = require('./utils/diceCalculator.js').default;
const { BeverageType, DICE_TYPES, DICE_TYPE_NAMES, BEER_TYPE_NAMES } = require('./utils/diceCalculator.js');

console.log('=== 测试重构后的骰子游戏功能 ===\n');

// 测试1: 验证常量定义
console.log('1. 测试常量定义:');
console.log('啤酒类型:', BeverageType);
console.log('点数类型:', DICE_TYPES);
console.log('点数类型中文名:', DICE_TYPE_NAMES);
console.log('啤酒类型中文名:', BEER_TYPE_NAMES);
console.log('');

// 测试2: 测试基础功能
console.log('2. 测试基础功能:');
const randomDice = diceCalculator.generateDice();
console.log('生成随机骰子:', randomDice);

const diceType = diceCalculator.getDiceType(randomDice);
console.log('骰子类型:', diceType, '中文名:', DICE_TYPE_NAMES[diceType]);

const weight = diceCalculator.calculateBeerWeight(diceType);
console.log('啤酒重量:', weight, '斤');
console.log('');

// 测试3: 测试散花生成
console.log('3. 测试散花生成:');
for (let i = 0; i < 5; i++) {
  const sanhuaDice = diceCalculator.generateSanhuaDice();
  const sanhuaType = diceCalculator.getDiceType(sanhuaDice);
  console.log(`散花测试 ${i+1}:`, sanhuaDice, '类型:', sanhuaType);
}
console.log('');

// 测试4: 测试毛利率控制算法
console.log('4. 测试毛利率控制算法:');

// 清空历史记录开始测试
diceCalculator.clearHistory();

// 模拟多次游戏，观察毛利率控制
for (let i = 0; i < 10; i++) {
  console.log(`\n--- 第 ${i+1} 次游戏 ---`);
  
  // 计算当前毛利率
  const stats = diceCalculator.calculateTotalStats();
  const currentMargin = diceCalculator.calculateProfitMargin(stats.totalRevenue, stats.totalCost);
  console.log(`当前毛利率: ${currentMargin.toFixed(2)}%`);
  
  // 进行游戏
  const result = diceCalculator.calculateGameResult(BeverageType.YUAN_JIANG);
  console.log('游戏结果:', {
    id: result.id,
    dice: `${result.dice[0]}-${result.dice[1]}-${result.dice[2]}`,
    diceType: DICE_TYPE_NAMES[result.diceType],
    weight: result.weight,
    revenue: result.revenue,
    cost: result.cost,
    profit: result.profit
  });
}

// 测试5: 验证最终数据格式
console.log('\n5. 验证最终数据格式:');
const history = diceCalculator.getHistory();
console.log('历史记录数量:', history.length);
if (history.length > 0) {
  console.log('最新记录格式:', history[history.length - 1]);
}

// 测试6: 验证总体统计
console.log('\n6. 验证总体统计:');
const finalStats = diceCalculator.calculateTotalStats();
const finalMargin = diceCalculator.calculateProfitMargin(finalStats.totalRevenue, finalStats.totalCost);
console.log('最终统计:', {
  totalRevenue: finalStats.totalRevenue,
  totalCost: finalStats.totalCost,
  totalProfit: finalStats.totalProfit,
  totalWeight: finalStats.totalWeight,
  profitMargin: finalMargin.toFixed(2) + '%'
});

console.log('\n=== 测试完成 ===');
