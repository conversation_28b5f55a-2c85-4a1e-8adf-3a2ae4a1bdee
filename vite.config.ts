/*
 * @Author: resty <EMAIL>
 * @Date: 2025-08-07 15:11:13
 * @LastEditors: resty <EMAIL>
 * @LastEditTime: 2025-08-07 19:21:02
 * @FilePath: /top.resty.dice_game/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __UNI_FEATURE_PROMISE__: false
  },
  server: {
    port: 3000,
    host: '0.0.0.0'
  },

})