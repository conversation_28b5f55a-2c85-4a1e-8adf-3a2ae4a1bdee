
/* 手机端默认样式 */
.modal-overlay[data-v-e3a2cddd] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 0.9375rem;
}
.modal-container[data-v-e3a2cddd] {
  background: white;
  border-radius: 0.625rem;
  width: 100%;
  max-width: 20.3125rem;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0.625rem 1.875rem rgba(0, 0, 0, 0.3);
}
.modal-header[data-v-e3a2cddd] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.78125rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}
.modal-title[data-v-e3a2cddd] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.close-btn[data-v-e3a2cddd] {
  background: none;
  border: none;
  font-size: 0.875rem;
  color: #999;
  padding: 0.25rem;
  border-radius: 50%;
  width: 1.5625rem;
  height: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-btn[data-v-e3a2cddd]:hover {
  background: #f5f5f5;
  color: #666;
}
.modal-cont
.rule-section[data-v-e3a2cddd] {
  margin-bottom: 0.9375rem;
  padding: 0.78125rem 0.3125rem;
  background: #f8f9fa;

  border-radius: 0.46875rem;
  width:calc(23.4375rem - 1.25rem)
}
.section-title[data-v-e3a2cddd] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 0.46875rem;
}
.rule-text[data-v-e3a2cddd] {
  font-size: 0.8125rem;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 0.3125rem;
}

/* 啤酒信息 */
.beer-info[data-v-e3a2cddd] {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
}
.beer-item[data-v-e3a2cddd] {
  font-size: 0.8125rem;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.46875rem;
  border-radius: 0.3125rem;
  display: block;
}

/* 点数类型 */
.dice-types[data-v-e3a2cddd] {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.46875rem;
}
.type-item[data-v-e3a2cddd] {
  background: rgba(255, 107, 107, 0.1);
  border-radius: 0.3125rem;
  padding: 0.46875rem;
  border-left: 0.1875rem solid #ff6b6b;
}
.type-name[data-v-e3a2cddd] {
  font-size: 0.8125rem;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
  margin-bottom: 0.25rem;
}
.type-desc[data-v-e3a2cddd] {
  font-size: 0.6875rem;
  color: #666;
  display: block;
  margin-bottom: 0.1875rem;
}
.type-reward[data-v-e3a2cddd] {
  font-size: 0.75rem;
  color: #27ae60;
  font-weight: bold;
  display: block;
}

/* 示例 */
.example[data-v-e3a2cddd] {
  background: rgba(39, 174, 96, 0.1);
  border-radius: 0.3125rem;
  padding: 0.46875rem;
  margin-top: 0.46875rem;
}
.example-title[data-v-e3a2cddd] {
  font-size: 0.75rem;
  font-weight: bold;
  color: #27ae60;
  display: block;
  margin-bottom: 0.3125rem;
}
.example-text[data-v-e3a2cddd] {
  font-size: 0.6875rem;
  color: #666;
  display: block;
  margin-bottom: 0.1875rem;
}

/* 平板适配 */
@media screen and (min-width: 768px) {
.modal-overlay[data-v-e3a2cddd] {
    padding: 1.875rem;
}
.modal-container[data-v-e3a2cddd] {
    max-width: 25rem;
}
.modal-header[data-v-e3a2cddd] {
    padding: 1.25rem;
}
.modal-title[data-v-e3a2cddd] {
    font-size: 1.25rem;
}
.close-btn[data-v-e3a2cddd] {
    font-size: 1.125rem;
    width: 2.1875rem;
    height: 2.1875rem;
}
.modal-content[data-v-e3a2cddd] {
    padding: 1.25rem;
}
.rule-section[data-v-e3a2cddd] {
    padding: 0.9375rem 0.78125rem;
}
.section-title[data-v-e3a2cddd] {
    font-size: 1rem;
}
.rule-text[data-v-e3a2cddd] {
    font-size: 0.875rem;
}
.dice-types[data-v-e3a2cddd] {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}
}

/* PC端适配 */
@media screen and (min-width: 1024px) {
.modal-overlay[data-v-e3a2cddd] {
    padding: 2.5rem;
}
.modal-container[data-v-e3a2cddd] {
    max-width: 28.125rem;
}
.modal-header[data-v-e3a2cddd] {
    padding: 1.5625rem;
}
.modal-title[data-v-e3a2cddd] {
    font-size: 1.375rem;
}
.close-btn[data-v-e3a2cddd] {
    font-size: 1.25rem;
    width: 2.5rem;
    height: 2.5rem;
    cursor: pointer;
}
.modal-content[data-v-e3a2cddd] {
    padding: 1.5625rem;
}
.rule-section[data-v-e3a2cddd] {
    padding: 1.09375rem 0.9375rem;
}
.section-title[data-v-e3a2cddd] {
    font-size: 1.125rem;
}
.rule-text[data-v-e3a2cddd] {
    font-size: 0.9375rem;
}
}


/* 遮罩层基础样式 */
.modal-overlay[data-v-07be8c39] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 0.625rem;
}

/* 全屏模式下的遮罩层样式 */
.modal-overlay.fullscreen[data-v-07be8c39] {
  padding: 0;
}

/* 弹窗容器基础样式 */
.modal-container[data-v-07be8c39] {
  background: white;
  border-radius: 0.625rem;
  width: 100%;
  max-width: 25rem;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 全屏模式下的弹窗容器样式 */
.modal-container.fullscreen[data-v-07be8c39] {
  max-width: none;
  max-height: none;
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
}

/* 弹窗头部样式 */
.modal-header[data-v-07be8c39] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.0625rem 0.46875rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  flex-shrink: 0;
}

/* 弹窗标题样式 */
.modal-title[data-v-07be8c39] {
  font-size: 0.5rem;
  font-weight: bold;
  color: white;
  flex: 1;
  height: 1.09375rem;
  line-height: 1.09375rem;
}

/* 关闭按钮样式 */
.close-btn[data-v-07be8c39] {
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}


/* 弹窗内容区域样式 */
.modal-content[data-v-07be8c39] {
  flex: 1;
  padding: 0;
  overflow: hidden;
}



/* 分析内容容器样式 */
.analysis-content[data-v-07be8c39] {
  padding: 0.3125rem 0.375rem 0.15625rem 0.15625rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);


  gap: 0;
}

/* 全屏模式下的分析内容容器样式 */
.analysis-content.fullscreen[data-v-07be8c39] {
  padding: 0.3125rem;
  height: calc(100vh - 2.5rem);
}

/* 统计行容器样式 */
.stats-row[data-v-07be8c39] {
  display: flex;
  gap: 0.125rem;
  margin-bottom: 0;
  margin-top: 0.125rem;
  flex-wrap: wrap;
  width: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.1875rem;
  padding: 0.125rem;
  border: 0.03125rem solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 第一行不需要上边距 */
.stats-row[data-v-07be8c39]:first-child {
  margin-top: 0;
}


/* 最后一行统计行的样式（移除底部边距） */
.stats-row[data-v-07be8c39]:last-child {
  margin-bottom: 0;
}

/* 统计卡片样式 */
.stat-card[data-v-07be8c39] {
  background: rgba(255, 255, 255, 0.5);
  padding: 0.25rem 0.1875rem;
  border-radius: 0.25rem;
  text-align: center;
  flex: 1;
  min-width: 0;
  border: 0.03125rem solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.stat-card[data-v-07be8c39]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.09375rem;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.stat-card[data-v-07be8c39]:hover {
  transform: translateY(-0.0625rem);
}

/* 第一行统计卡片特殊样式 */
.stats-row:first-child .stat-card[data-v-07be8c39] {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0.125rem 0.09375rem;
}

/* 啤酒统计行样式 */
.beer-stat-row[data-v-07be8c39] {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0.1875rem;
  width: 100%;
  gap: 0.125rem;
}
.beer-stat-row[data-v-07be8c39]:last-child {
  margin-bottom: 0;
}

/* 啤酒统计项样式 */
.beer-stat-item[data-v-07be8c39] {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 0.15625rem 0.1875rem;
  margin: 0;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.125rem;
  border: 0.03125rem solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}
.beer-stat-item[data-v-07be8c39]:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.02);
}

/* 点数类型标题样式 */
.dice-type-title[data-v-07be8c39] {
  font-size: 0.375rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 0.09375rem;
  padding: 0.0625rem 0;
  border-bottom: 0.03125rem solid rgba(102, 126, 234, 0.3);
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 0.0625rem;
}

/* 点数统计行样式 */
.dice-stat-row[data-v-07be8c39] {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0.125rem;
  width: 100%;
  gap: 0.09375rem;
}
.dice-stat-row[data-v-07be8c39]:last-child {
  margin-bottom: 0;
}

/* 点数统计项样式 */
.dice-stat-item[data-v-07be8c39] {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0.125rem 0.09375rem;
  margin: 0;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.09375rem;
  border: 0.03125rem solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-height: 0.9375rem;
}
.dice-stat-item[data-v-07be8c39]:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

/* 补充统计行样式 */
.supplement-stat-row[data-v-07be8c39] {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0;
  width: 100%;
  gap: 0.15625rem;
}

/* 补充统计项样式 */
.supplement-stat-item[data-v-07be8c39] {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0.1875rem 0.125rem;
  margin: 0;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.125rem;
  border: 0.03125rem solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-height: 1.09375rem;
}
.supplement-stat-item[data-v-07be8c39]:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-0.03125rem);
}

/* 统计标签文字样式 */
.stat-label[data-v-07be8c39] {
  font-size: 0.4375rem;
  color: #666;
  display: block;
  margin-bottom: 0.0625rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  font-weight: 500;
}

/* 统计值文字样式 */
.stat-value[data-v-07be8c39] {
  font-size: 0.5rem;
  font-weight: bold;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  flex: 1;
  margin-left: 0.125rem;
}

/* 收入数值样式 */
.stat-value.revenue[data-v-07be8c39] {
  color: #27ae60;
}

/* 成本数值样式 */
.stat-value.cost[data-v-07be8c39] {
  color: #e74c3c;
}

/* 盈利数值样式 */
.stat-value.profit[data-v-07be8c39] {
  color: #27ae60;
}

/* 负盈利数值样式 */
.stat-value.profit.negative[data-v-07be8c39] {
  color: #e74c3c;
}

/* 高利润率样式 */
.stat-value.margin.good[data-v-07be8c39] {
  color: #27ae60;
}

/* 中等利润率样式 */
.stat-value.margin.warning[data-v-07be8c39] {
  color: #f39c12;
}

/* 低利润率样式 */
.stat-value.margin.danger[data-v-07be8c39] {
  color: #e74c3c;
}

/* 原浆啤酒卡片样式 */
.stat-card.beer-draft[data-v-07be8c39] {
  border-left: 0.1875rem solid #ff6b35;
  background: linear-gradient(135deg, #fff5f2 0%, #ffe8e0 100%);
}
.stat-card.beer-draft[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #ff6b35 0%, #ff8a65 100%);
}

/* 果啤卡片样式 */
.stat-card.beer-fruit[data-v-07be8c39] {
  border-left: 0.1875rem solid #4caf50;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}
.stat-card.beer-fruit[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

/* 散花点数卡片样式 */
.stat-card.dice-sanhua[data-v-07be8c39] {
  border-left: 0.1875rem solid #2196f3;
  background: linear-gradient(135deg, #e3f2fd 0%, #e1f5fe 100%);
}
.stat-card.dice-sanhua[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #2196f3 0%, #42a5f5 100%);
}

/* 对子点数卡片样式 */
.stat-card.dice-duizi[data-v-07be8c39] {
  border-left: 0.1875rem solid #4caf50;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
}
.stat-card.dice-duizi[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

/* 顺子点数卡片样式 */
.stat-card.dice-shunzi[data-v-07be8c39] {
  border-left: 0.1875rem solid #ff9800;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}
.stat-card.dice-shunzi[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
}

/* 豹子点数卡片样式 */
.stat-card.dice-baozi[data-v-07be8c39] {
  border-left: 0.1875rem solid #e91e63;
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
}
.stat-card.dice-baozi[data-v-07be8c39]::before {
  background: linear-gradient(90deg, #e91e63 0%, #f06292 100%);
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  /* 移动端遮罩层样式 */
.modal-overlay[data-v-07be8c39] {
    padding: 0.15625rem;
}

  /* 移动端全屏模式下的遮罩层样式 */
.modal-overlay.fullscreen[data-v-07be8c39] {
    padding: 0;
}

  /* 移动端弹窗容器样式 */
.modal-container[data-v-07be8c39] {
    max-height: 90vh;
}

  /* 移动端全屏模式下的弹窗容器样式 */
.modal-container.fullscreen[data-v-07be8c39] {
    max-height: none;
    height: 100vh;
}

  /* 移动端弹窗头部样式 */
.modal-header[data-v-07be8c39] {
    padding: 0.03125rem 0.15625rem;
}

  /* 移动端弹窗标题样式 */
.modal-title[data-v-07be8c39] {
    font-size: 0.4375rem;
}

  /* 移动端关闭按钮样式 */
.close-btn[data-v-07be8c39] {
    width: 0.46875rem;
    height: 0.46875rem;
    font-size: 0.3125rem;
}

  /* 移动端分析内容容器样式 */
.analysis-content[data-v-07be8c39] {
    padding: 0.15625rem;
}

  /* 移动端全屏模式下的分析内容容器样式 */
.analysis-content.fullscreen[data-v-07be8c39] {
    padding: 0.15625rem;
    height: calc(100vh - 1.875rem);
}

  /* 移动端统计行容器样式 */
.stats-row[data-v-07be8c39] {
    gap: 0.0625rem;
    margin-top: 0.0625rem;
    border-radius: 0.125rem;
    padding: 0.0625rem;
    background: rgba(255, 255, 255, 0.5);
}

  /* 移动端统计卡片样式 */
.stat-card[data-v-07be8c39] {
    padding: 0.125rem 0.09375rem;
    border-radius: 0.1875rem;
    background: rgba(255, 255, 255, 0.5);
}

  /* 移动端统计行上边距 */
.stats-row[data-v-07be8c39] {
    margin-top: 0.09375rem;
}

  /* 移动端统计标签文字样式 */
.stat-label[data-v-07be8c39] {
    font-size: 0.375rem;
}

  /* 移动端统计值文字样式 */
.stat-value[data-v-07be8c39] {
    font-size: 0.4375rem;
}

  /* 移动端补充统计行样式 */
.supplement-stat-row[data-v-07be8c39] {
    gap: 0.09375rem;
}

  /* 移动端补充统计项样式 */
.supplement-stat-item[data-v-07be8c39] {
    padding: 0.09375rem 0.0625rem;
    border-radius: 0.0625rem;
    min-height: 0.625rem;
}

  /* 移动端点数类型标题 */
.dice-type-title[data-v-07be8c39] {
    font-size: 0.3125rem;
}
}

/* 平板端适配 */
@media screen and (min-width: 600px) {
  /* 平板端全屏模式下的弹窗容器样式 */
.modal-container.fullscreen[data-v-07be8c39] {
    width: 100vw;
    height: 100vh;
}
  
  /* 平板端全屏模式下的分析内容容器样式 */
.analysis-content.fullscreen[data-v-07be8c39] {
    padding: 0.25rem;
    height: calc(100vh - 3.125rem);
}
  
  /* 平板端统计行容器样式 */
.stats-row[data-v-07be8c39] {
    gap: 0.09375rem;
    margin-top: 0.09375rem;
    border-radius: 0.15625rem;
    padding: 0.09375rem;
    background: rgba(255, 255, 255, 0.5);
}

  /* 平板端统计标签文字样式 */
.stat-label[data-v-07be8c39] {
    font-size: 0.5rem;
}

  /* 平板端统计值文字样式 */
.stat-value[data-v-07be8c39] {
    font-size: 0.5625rem;
}

  /* 平板端补充统计行样式 */
.supplement-stat-row[data-v-07be8c39] {
    gap: 0.125rem;
}

  /* 平板端补充统计项样式 */
.supplement-stat-item[data-v-07be8c39] {
    padding: 0.125rem 0.09375rem;
    border-radius: 0.09375rem;
    min-height: 0.78125rem;
}

  /* 平板端统计行上边距 */
.stats-row[data-v-07be8c39] {
    margin-top: 0.125rem;
}
  
  /* 平板端点数类型标题 */
.dice-type-title[data-v-07be8c39] {
    font-size: 0.4375rem;
}
}

/* PC端适配 */
@media screen and (min-width: 1024px) {
  /* PC端遮罩层样式 */
.modal-overlay[data-v-07be8c39] {
    padding: 0.9375rem;
}
  
  /* PC端全屏模式下的遮罩层样式 */
.modal-overlay.fullscreen[data-v-07be8c39] {
    padding: 0;
}

  /* PC端弹窗容器样式 */
.modal-container[data-v-07be8c39] {
    max-width: 28.125rem;
}
  
  /* PC端全屏模式下的弹窗容器样式 */
.modal-container.fullscreen[data-v-07be8c39] {
    max-width: none;
    width: 100vw;
    height: 100vh;
}
  
  /* PC端弹窗头部样式 */
.modal-header[data-v-07be8c39] {
    padding: 0.3125rem 1.25rem;
}
  
  /* PC端弹窗标题样式 */
.modal-title[data-v-07be8c39] {
    font-size: 1.125rem;
}
  
  /* PC端分析内容容器样式 */
.analysis-content[data-v-07be8c39] {
    padding: 0.3125rem;
}
  
  /* PC端全屏模式下的分析内容容器样式 */
.analysis-content.fullscreen[data-v-07be8c39] {
    padding: 0.3125rem;
    height: calc(100vh - 4.6875rem);
}
  
  /* PC端统计行容器样式 */
.stats-row[data-v-07be8c39] {
    gap: 0.125rem;
    margin-top: 0.125rem;
    border-radius: 0.1875rem;
    padding: 0.125rem;
    background: rgba(255, 255, 255, 0.5);
}

  /* PC端统计标签文字样式 */
.stat-label[data-v-07be8c39] {
    font-size: 0.8125rem;
}

  /* PC端统计值文字样式 */
.stat-value[data-v-07be8c39] {
    font-size: 0.9375rem;
}

  /* PC端补充统计行样式 */
.supplement-stat-row[data-v-07be8c39] {
    gap: 0.15625rem;
}

  /* PC端补充统计项样式 */
.supplement-stat-item[data-v-07be8c39] {
    padding: 0.1875rem 0.125rem;
    border-radius: 0.125rem;
    min-height: 1.09375rem;
}

  /* PC端统计行上边距 */
.stats-row[data-v-07be8c39] {
    margin-top: 0.1875rem;
}
.stats-row[data-v-07be8c39]:first-child {
    margin-top: 0;
}

  /* PC端点数类型标题 */
.dice-type-title[data-v-07be8c39] {
    font-size: 0.5rem;
}
}


.cube-container[data-v-f0b1c87e] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--cube-size, 1.875rem);
  height: var(--cube-size, 1.875rem);
  min-width: var(--cube-size, 1.875rem);
  min-height: var(--cube-size, 1.875rem);
  /* 确保3D透视正确 */
  perspective-origin: center center;
}
.cube-scene[data-v-f0b1c87e] {
  position: relative;
  /* 3D设置 - 保持所有面可见 */
  transform-style: preserve-3d;
  transform-origin: center center center;
  /* 移除过渡，避免与动画冲突 */
  /* transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1); */
}
.face-element[data-v-f0b1c87e] {
  position: absolute;
  border-radius: 15%;
  /* 真实象牙白背景 */
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 50%, #f0f0f0 100%);
  /* 简化的圆柱边缘效果 */
  border: 0.03125rem solid #e0e0e0;
  /* 简化的立体阴影 */
  box-shadow: 0 0 0 0.03125rem #d0d0d0, 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.12),
    0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.08), inset 0 0.03125rem 0.03125rem rgba(255, 255, 255, 0.8),
    inset 0 -0.03125rem 0.03125rem rgba(0, 0, 0, 0.05);
  /* 基础flex布局 */
  display: flex;
  transition: all 0.3s ease;
  /* 关键：防止透视穿透和修复圆角问题 */
  overflow: hidden;
  box-sizing: border-box;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden; /* 隐藏背面，避免看到直角白底 */
  opacity: 1;
  /* 确保圆角正确显示 */
  transform-style: preserve-3d;
}

/* 点的基础样式 - 真实骰子的圆锥凹陷效果 */
.dot[data-v-f0b1c87e] {
  border-radius: 50%;
  /* 真实骰子点的圆锥凹陷效果 */
  box-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.3),
    inset 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.6), inset 0 -0.0625rem 0.09375rem rgba(0, 0, 0, 0.4),
    inset 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.5), inset 0 0 0.125rem rgba(0, 0, 0, 0.7),
    inset 0 -0.03125rem 0.03125rem rgba(255, 255, 255, 0.1);
  /* 默认蓝色凹陷背景 - 圆锥形渐变 */
  background: radial-gradient(
    circle at center,
    #0d47a1 0%,
    #1565c0 20%,
    #1976d2 40%,
    #1e88e5 60%,
    #2196f3 80%,
    #42a5f5 100%
  );
  transition: all 0.3s ease;
  /* 确保点数完全圆形 */
  aspect-ratio: 1;
}

/* 红色点数样式 - 圆锥形凹陷 */
.dot.red-dot[data-v-f0b1c87e] {
  background: radial-gradient(
    circle at center,
    #b71c1c 0%,
    #c62828 20%,
    #d32f2f 40%,
    #e53935 60%,
    #f44336 80%,
    #ef5350 100%
  ) !important;
}

/* 蓝色点数样式 */
.dot.blue-dot[data-v-f0b1c87e] {
  background: radial-gradient(
    circle at center,
    #0d47a1 0%,
    #1565c0 20%,
    #1976d2 40%,
    #1e88e5 60%,
    #2196f3 80%,
    #42a5f5 100%
  ) !important;
}

/* 1点 - 居中 */
.dice-face-1[data-v-f0b1c87e] {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 2点 - 对角线布局（左上到右下） */
.dice-face-2[data-v-f0b1c87e] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.dice-face-2 .dot[data-v-f0b1c87e]:nth-child(2) {
  align-self: flex-end;
}

/* 3点 - 对角线布局 + 中心点 */
.dice-face-3[data-v-f0b1c87e] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.dice-face-3 .dot[data-v-f0b1c87e]:nth-child(2) {
  align-self: center;
}
.dice-face-3 .dot[data-v-f0b1c87e]:nth-child(3) {
  align-self: flex-end;
}

/* 4点 - 两列布局，每列两个点垂直排列 */
.dice-face-4[data-v-f0b1c87e] {
  display: flex;
  justify-content: space-around;
  align-items: stretch;
}
.dice-face-4 .dot[data-v-f0b1c87e] {
  position: absolute;
}
.dice-face-4 .dot[data-v-f0b1c87e]:nth-child(1) {
  top: calc(25% - var(--dot-size, 0.25rem) / 2);
  left: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-4 .dot[data-v-f0b1c87e]:nth-child(2) {
  top: calc(25% - var(--dot-size, 0.25rem) / 2);
  right: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-4 .dot[data-v-f0b1c87e]:nth-child(3) {
  bottom: calc(25% - var(--dot-size, 0.25rem) / 2);
  left: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-4 .dot[data-v-f0b1c87e]:nth-child(4) {
  bottom: calc(25% - var(--dot-size, 0.25rem) / 2);
  right: calc(25% - var(--dot-size, 0.25rem) / 2);
}

/* 5点 - 四角 + 中心点 */
.dice-face-5[data-v-f0b1c87e] {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  position: relative;
}
.dice-face-5 .dot[data-v-f0b1c87e]:nth-child(1) {
  position: absolute;
  top: calc(25% - var(--dot-size, 0.25rem) / 2);
  left: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-5 .dot[data-v-f0b1c87e]:nth-child(2) {
  position: absolute;
  top: calc(25% - var(--dot-size, 0.25rem) / 2);
  right: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-5 .dot[data-v-f0b1c87e]:nth-child(3) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.dice-face-5 .dot[data-v-f0b1c87e]:nth-child(4) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 0.25rem) / 2);
  left: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-5 .dot[data-v-f0b1c87e]:nth-child(5) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 0.25rem) / 2);
  right: calc(25% - var(--dot-size, 0.25rem) / 2);
}

/* 6点 - 三行布局，每行两个点 */
.dice-face-6[data-v-f0b1c87e] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
}
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(1),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(2) {
  position: absolute;
  top: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(3),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(4) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(5),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(6) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(1),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(3),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(5) {
  left: calc(25% - var(--dot-size, 0.25rem) / 2);
}
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(2),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(4),
.dice-face-6 .dot[data-v-f0b1c87e]:nth-child(6) {
  right: calc(25% - var(--dot-size, 0.25rem) / 2);
}

/* 立方体面的3D定位 - 真实骰子标准（相对面相加等于7） */
.face-1[data-v-f0b1c87e] {
  transform: translateZ(var(--half-cube-size, 1.25rem)); /* 前面 - 1点 */
  z-index: 1;
}
.face-2[data-v-f0b1c87e] {
  transform: rotateY(180deg) translateZ(var(--half-cube-size, 1.25rem)); /* 后面 - 6点（1的对面） */
  z-index: 1;
}
.face-3[data-v-f0b1c87e] {
  transform: rotateY(90deg) translateZ(var(--half-cube-size, 1.25rem)); /* 右面 - 3点 */
  z-index: 1;
}
.face-4[data-v-f0b1c87e] {
  transform: rotateY(-90deg) translateZ(var(--half-cube-size, 1.25rem)); /* 左面 - 4点（3的对面） */
  z-index: 1;
}
.face-5[data-v-f0b1c87e] {
  transform: rotateX(90deg) translateZ(var(--half-cube-size, 1.25rem)); /* 上面 - 2点 */
  z-index: 1;
}
.face-6[data-v-f0b1c87e] {
  transform: rotateX(-90deg) translateZ(var(--half-cube-size, 1.25rem)); /* 下面 - 5点（2的对面） */
  z-index: 1;
}
.control-btn[data-v-f0b1c87e]:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.25) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );

  box-shadow: 0 0.1875rem 0.5rem rgba(0, 0, 0, 0.2), 0 0.09375rem 0.25rem rgba(0, 0, 0, 0.15),
    inset 0 0.03125rem 0 rgba(255, 255, 255, 0.5);

  transform: translateY(-0.0625rem);
}
.control-btn[data-v-f0b1c87e]:active {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );

  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.2),
    inset 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);

  transform: translateY(0.03125rem) scale(0.98);
}
.info[data-v-f0b1c87e] {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);

  /* 精致背景 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  padding: 0.78125rem 1.09375rem;
  border-radius: 0.625rem;
  border: 0.03125rem solid rgba(255, 255, 255, 0.15);

  backdrop-filter: blur(0.3125rem);
  -webkit-backdrop-filter: blur(0.3125rem);

  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1),
    inset 0 0.03125rem 0 rgba(255, 255, 255, 0.2);

  max-width: 18.75rem;
  margin: 0 auto;
}
.info uni-text[data-v-f0b1c87e] {
  display: block;
  font-size: 0.6875rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
  font-weight: 400;
}
.info uni-text[data-v-f0b1c87e]:first-child {
  font-size: 0.8125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 0.375rem;
}
.info uni-text[data-v-f0b1c87e]:last-child {
  margin-bottom: 0;
}
.face-controls[data-v-f0b1c87e] {
  display: flex;
  gap: 0.46875rem;
  margin-bottom: 0.9375rem;
  flex-wrap: wrap;
  justify-content: center;
}
.face-btn[data-v-f0b1c87e] {
  padding: 0.375rem 0.625rem;

  /* 精致玻璃质感 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );

  color: rgba(255, 255, 255, 0.85);
  border: 0.03125rem solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5625rem;
  font-size: 0.625rem;
  font-weight: 500;

  backdrop-filter: blur(0.3125rem);
  -webkit-backdrop-filter: blur(0.3125rem);

  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1),
    inset 0 0.03125rem 0 rgba(255, 255, 255, 0.2);

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}
.face-btn[data-v-f0b1c87e]:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%
  );

  transform: translateY(-0.03125rem);
}
.face-btn.active[data-v-f0b1c87e] {
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.4) 0%,
    rgba(255, 235, 59, 0.3) 100%
  );

  border-color: rgba(255, 215, 0, 0.6);
  color: #ffd700;

  box-shadow: 0 0 0.46875rem rgba(255, 215, 0, 0.3), 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15),
    inset 0 0.03125rem 0 rgba(255, 255, 255, 0.3);
}
.face-btn[data-v-f0b1c87e]:active {
  transform: translateY(0.03125rem) scale(0.96);
}
.face-controls[data-v-f0b1c87e] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.46875rem;
  margin-bottom: 0.9375rem;
  justify-content: center;
}
.face-btn[data-v-f0b1c87e] {
  padding: 0.46875rem 0.78125rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.625rem;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}
.face-btn.active[data-v-f0b1c87e] {
  background: rgba(255, 235, 59, 0.3);
  border-color: #ffeb3b;
  color: #ffeb3b;
}
.face-btn[data-v-f0b1c87e]:active {
  transform: scale(0.95);
}

/* ================================= 移动端样式 (默认) ================================= */
.game-container[data-v-1cf27b2a] {
  min-height: 100vh;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "middle"
    "bottom";
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.625rem;
  gap: 0.625rem;
}

/* 上部：标题栏 */
.header-area[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.1);
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.9375rem;
  border-radius: 0.46875rem;
}
.rules-btn[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.625rem 0.9375rem;
  border-radius: 0.78125rem;
  font-size: 0.75rem;
  flex-shrink: 0;
}
.header-buttons[data-v-1cf27b2a] {
  display: flex;
  gap: 0.46875rem;
  flex-shrink: 0;
}
.header-btn[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
  padding: 0.46875rem 0.78125rem;
  border-radius: 0.625rem;
  font-size: 0.6875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
}
.header-btn[data-v-1cf27b2a]:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-0.0625rem);
}
.title-section[data-v-1cf27b2a] {
  text-align: center;
  flex: 1;
}
.main-title[data-v-1cf27b2a] {
  font-size: 1.125rem;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 0.25rem;
}
.sub-title[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 中部：三栏布局 */
.middle-area[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.05);
  grid-area: middle;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "rules"
    "game"
    "history";
  gap: 0.46875rem;
  padding: 0.625rem;
  border-radius: 0.46875rem;
}

/* 左：游戏规则面板 */
.rules-panel[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.1);
  grid-area: rules;
  padding: 0.625rem;
  border-radius: 0.3125rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.panel-header[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.46875rem;
}
.panel-title[data-v-1cf27b2a] {
  font-size: 0.875rem;
  font-weight: bold;
  color: white;
  display: block;
}
.record-count[data-v-1cf27b2a] {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}
.action-btn[data-v-1cf27b2a] {
  padding: 0.1875rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.5625rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.clear-btn[data-v-1cf27b2a] {
  background: rgba(255, 107, 107, 0.8);
  color: white;
}
.clear-btn[data-v-1cf27b2a]:hover {
  background: rgba(255, 107, 107, 1);
  transform: translateY(-0.03125rem);
}
.analysis-btn[data-v-1cf27b2a] {
  background: rgba(103, 126, 234, 0.8);
  color: white;
}
.analysis-btn[data-v-1cf27b2a]:hover {
  background: rgba(103, 126, 234, 1);
  transform: translateY(-0.03125rem);
}
.rules-content[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  flex: 1;
}
.rule-section[data-v-1cf27b2a] {
  margin-bottom: 0.46875rem;
}
.section-subtitle[data-v-1cf27b2a] {
  font-size: 0.625rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
  display: block;
  margin-bottom: 0.25rem;
  text-align: center;
}
.rule-list[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  gap: 0.1875rem;
}
.rule-item-wrapper[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 0.1875rem;
  padding: 0.25rem 0.1875rem;
  margin-bottom: 0.125rem;
}
.rule-item[data-v-1cf27b2a] {
  font-size: 0.5625rem;
  font-weight: bold;
  display: block;
  margin-bottom: 0.0625rem;
}
.rule-desc[data-v-1cf27b2a] {
  font-size: 0.4375rem;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-bottom: 0.0625rem;
  text-align: center;
}
.rule-reward[data-v-1cf27b2a] {
  font-size: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  display: block;
  text-align: center;
}
.rule-extra[data-v-1cf27b2a] {
  font-size: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  text-align: center;
  margin-bottom: 0.125rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.1875rem;
  border-radius: 0.125rem;
}

/* 游戏规则字体颜色 - 基于紫色渐变背景优化 */
.rule-sanhua[data-v-1cf27b2a] {
  color: #4FC3F7 !important;
  /* 散花 - 天蓝色，与豹子粉色形成明显区分 */
}
.rule-duizi[data-v-1cf27b2a] {
  color: #81C784 !important;
  /* 对子 - 清新绿色，与紫色形成互补 */
}
.rule-shunzi[data-v-1cf27b2a] {
  color: #FFB74D !important;
  /* 顺子 - 温暖橙色，增加活力 */
}
.rule-baozi[data-v-1cf27b2a] {
  color: #F48FB1 !important;
  /* 豹子 - 粉红色，保持温和但突出 */
}

/* 中：游戏区域 */
.game-area[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.08);
  grid-area: game;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.9375rem;
  border-radius: 0.3125rem;
  min-height: 9.375rem; /* 确保游戏区域有最小高度 */
}
.dice-container[data-v-1cf27b2a] {
  display: flex;
  gap: 0.625rem;
  margin-bottom: 0.9375rem;
  align-items: center;
  justify-content: center;
  min-height: 3.75rem; /* 确保有足够高度显示骰子 */
  padding: 0.625rem 0; /* 添加上下内边距 */
}

/* 确保骰子之间有间距 */
.dice-container > *[data-v-1cf27b2a] {
  margin: 0 1.25rem;
}
.dice-container > *[data-v-1cf27b2a]:first-child {
  margin-left: 0;
}
.dice-container > *[data-v-1cf27b2a]:last-child {
  margin-right: 0;
}
.game-result[data-v-1cf27b2a] {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.625rem;
  border-radius: 0.3125rem;
  width: 90%; /* 调整为90%宽度，留出更多空间 */
  max-width: 15.625rem; /* 限制最大宽度 */
  margin: 0.625rem auto 0; /* 居中显示 */
  min-height: 1.875rem; /* 固定最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}
.result-content[data-v-1cf27b2a] {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 0.3125rem; /* 内容区域固定高度 */
}
.result-prompt[data-v-1cf27b2a] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
  line-height: 1.2;
}
.result-rolling[data-v-1cf27b2a] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #ffa500;
  display: block;
  line-height: 1.2;
  animation: rolling-pulse-1cf27b2a 1s ease-in-out infinite;
}
@keyframes rolling-pulse-1cf27b2a {
0%, 100% {
    opacity: 1;
    transform: scale(1);
}
50% {
    opacity: 0.7;
    transform: scale(1.05);
}
}
.dice-display[data-v-1cf27b2a] {
  margin-bottom: 0.0625rem;
}
.dice-numbers[data-v-1cf27b2a] {
  font-size: 0.75rem;
  font-weight: bold;
  color: #333;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.125rem 0.375rem;
  border-radius: 0.46875rem;
  display: inline-block;
}
.result-type[data-v-1cf27b2a] {
  font-size: 0.5625rem;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
  margin-bottom: 0.0625rem;
}

/* 游戏结果类型颜色 - 与游戏规则保持一致 */
.result-sanhua[data-v-1cf27b2a] {
  color: #4FC3F7 !important; /* 散花 - 天蓝色 */
}
.result-duizi[data-v-1cf27b2a] {
  color: #81C784 !important; /* 对子 - 清新绿色 */
}
.result-shunzi[data-v-1cf27b2a] {
  color: #FFB74D !important; /* 顺子 - 温暖橙色 */
}
.result-baozi[data-v-1cf27b2a] {
  color: #F48FB1 !important; /* 豹子 - 粉红色 */
}
.result-weight[data-v-1cf27b2a],
.result-beer[data-v-1cf27b2a] {
  font-size: 0.625rem;
  color: #666;
  display: block;
  margin-bottom: 0.15625rem;
}
.result-profit[data-v-1cf27b2a] {
  font-size: 0.6875rem;
  font-weight: bold;
  color: #27ae60;
  display: block;
}

/* 右：游戏记录面板 */
.history-panel[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.1);
  grid-area: history;
  padding: 0.625rem;
  border-radius: 0.3125rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.history-content[data-v-1cf27b2a] {
  flex: 1;
  overflow-y: auto;
}
.no-history[data-v-1cf27b2a] {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.6875rem;
  padding: 0.625rem;
}
.history-list[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.history-item[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.375rem;
  border-radius: 0.1875rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.125rem;
}
.history-dice-container[data-v-1cf27b2a] {
  display: flex;
  gap: 0.125rem;
  align-items: center;
}
.history-dice-single[data-v-1cf27b2a] {
  width: 0.75rem;
  height: 0.75rem;
  background: white;
  border-radius: 0.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
  border: 0.03125rem solid rgba(0, 0, 0, 0.1);
}
.history-dice-number[data-v-1cf27b2a] {
  font-size: 0.4375rem;
  font-weight: bold;
  color: #333;
  line-height: 1;
}
.history-type[data-v-1cf27b2a] {
  font-size: 0.5625rem;
  color: white;
  font-weight: bold;
}

/* 游戏记录背景颜色 - 与新颜色方案协调 */
.history-sanhua[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #4FC3F7 0%, #2196F3 100%) !important;
  /* 散花 - 蓝色渐变背景 */
}
.history-sanhua .history-type[data-v-1cf27b2a] {
  color: white !important;
  /* 白色字体在紫色背景上 */
}
.history-duizi[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%) !important;
  /* 对子 - 绿色渐变背景 */
}
.history-duizi .history-type[data-v-1cf27b2a] {
  color: white !important;
  /* 白色字体在绿色背景上 */
}
.history-shunzi[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%) !important;
  /* 顺子 - 橙色渐变背景 */
}
.history-shunzi .history-type[data-v-1cf27b2a] {
  color: white !important;
  /* 白色字体在橙色背景上 */
}
.history-baozi[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #F48FB1 0%, #E91E63 100%) !important;
  /* 豹子 - 粉色渐变背景 */
}
.history-baozi .history-type[data-v-1cf27b2a] {
  color: white !important;
  /* 白色字体在粉色背景上 */
}

/* 下部：啤酒选择按钮 */
.bottom-area[data-v-1cf27b2a] {
  background: rgba(255, 255, 255, 0.1);
  grid-area: bottom;
  position: relative;
  padding: 0.78125rem;
  border-radius: 0.46875rem;
}
.bottom-actions[data-v-1cf27b2a] {
  position: absolute;
  top: 0.46875rem;
  right: 0.46875rem;
  display: flex;
  gap: 0.25rem;
  z-index: 10;
}
.beer-buttons[data-v-1cf27b2a] {
  display: grid;
  grid-template-columns: 1fr 3fr 2fr 3fr 1fr;
  gap: 0;
  width: 100%;
}
.beer-btn[data-v-1cf27b2a] {
  padding: 0;
  border-radius: 0.625rem;
  border: 0.0625rem solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.8125rem;
  font-weight: bold;
  transition: all 0.2s ease;
}
.btn-content[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  padding: 0.78125rem 0.625rem;
  height: 100%;
}
.btn-icon[data-v-1cf27b2a] {
  font-size: 1rem;
}
.btn-text[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
  line-height: 1;
}
.btn-title[data-v-1cf27b2a] {
  font-size: 0.6875rem;
  font-weight: bold;
  color: white;
  line-height: 1;
  margin-bottom: -0.0625rem;
}
.btn-price[data-v-1cf27b2a] {
  font-size: 0.5625rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: normal;
  line-height: 1;
}
.beer-buttons .draft-btn[data-v-1cf27b2a] {
  grid-column: 2;
}
.beer-buttons .fruit-btn[data-v-1cf27b2a] {
  grid-column: 4;
}
.beer-btn.active[data-v-1cf27b2a] {
  border-color: rgba(255, 255, 255, 0.8);
}
.draft-btn.active[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 0.25rem 0.78125rem rgba(102, 126, 234, 0.4);
  transform: translateY(-0.125rem);
}
.draft-btn.active .btn-title[data-v-1cf27b2a] {
  color: white;
}
.draft-btn.active .btn-price[data-v-1cf27b2a] {
  color: rgba(255, 255, 255, 0.9);
}
.fruit-btn.active[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 0.25rem 0.78125rem rgba(102, 126, 234, 0.4);
  transform: translateY(-0.125rem);
}
.fruit-btn.active .btn-title[data-v-1cf27b2a] {
  color: white;
}
.fruit-btn.active .btn-price[data-v-1cf27b2a] {
  color: rgba(255, 255, 255, 0.9);
}

/* 按钮禁用状态 */
.beer-btn.disabled[data-v-1cf27b2a],
.beer-btn[data-v-1cf27b2a]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  transform: none !important;
  box-shadow: none !important;
}

/* ================================= 平板样式 (600px+) ================================= */
@media screen and (min-width: 600px) {
.game-container[data-v-1cf27b2a] {
    height: 100vh;
    display: grid;
    grid-template-rows: auto 1fr auto;
    grid-template-areas:
      "header"
      "middle"
      "bottom";
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0.3125rem;
    gap: 0.3125rem;
    box-sizing: border-box;
    overflow: hidden;
}

  /* 上部：标题栏 */
.header-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.46875rem 0.625rem;
    border-radius: 0.3125rem;
    box-sizing: border-box;
    min-height: 1.875rem;
}
.rules-btn[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.46875rem;
    font-size: 0.5rem;
    cursor: pointer;
    white-space: nowrap;
    min-width: 2.5rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
.rules-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-0.03125rem);
}
.header-buttons[data-v-1cf27b2a] {
    display: flex;
    gap: 0.25rem;
    flex-shrink: 0;
}
.header-btn[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 0.03125rem solid rgba(255, 255, 255, 0.3);
    padding: 0.1875rem 0.375rem;
    border-radius: 0.375rem;
    font-size: 0.4375rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
.header-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-0.03125rem);
}
.title-section[data-v-1cf27b2a] {
    text-align: center;
    flex: 1;
    margin: 0 0.46875rem;
}
.main-title[data-v-1cf27b2a] {
    font-size: 0.625rem;
    font-weight: bold;
    color: white;
    display: block;
    margin-bottom: 0.0625rem;
    line-height: 1.1;
}
.sub-title[data-v-1cf27b2a] {
    font-size: 0.4375rem;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    line-height: 1.1;
}

  /* 中部：三栏布局 */
.middle-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.05);
    grid-area: middle;
    display: grid;
    grid-template-columns: 3.75rem 1fr 3.75rem;
    grid-template-rows: 1fr;
    grid-template-areas: "rules game history";
    gap: 0.3125rem;
    padding: 0.3125rem;
    border-radius: 0.3125rem;
    box-sizing: border-box;
    transition: grid-template-columns 0.3s ease;
    align-items: start; /* 改为顶部对齐，不拉伸 */
    max-height: 18.75rem; /* 限制中间区域最大高度 */
    overflow: hidden;
}

  /* 隐藏规则面板时的布局 */
.middle-area.hide-rules[data-v-1cf27b2a] {
    grid-template-columns: 0 1fr 3.75rem;
}

  /* 隐藏历史面板时的布局 */
.middle-area.hide-history[data-v-1cf27b2a] {
    grid-template-columns: 3.75rem 1fr 0;
}

  /* 隐藏两个面板时的布局 */
.middle-area.hide-both[data-v-1cf27b2a] {
    grid-template-columns: 0 1fr 0;
}

  /* 左：游戏规则面板 */
.rules-panel[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: rules;
    padding: 0.3125rem;
    border-radius: 0.25rem;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: auto; /* 改为自动高度 */
    min-height: 9.375rem; /* 设置最小高度 */
    max-height: 12.5rem; /* 限制最大高度，与其他面板一致 */
}
.panel-header[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.25rem;
}
.panel-title[data-v-1cf27b2a] {
    font-size: 0.5rem;
    font-weight: bold;
    color: white;
    display: block;
    text-align: center;
}
.record-count[data-v-1cf27b2a] {
    font-size: 0.375rem;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    text-align: center;
}
.action-btn[data-v-1cf27b2a] {
    padding: 0.09375rem 0.1875rem;
    border-radius: 0.1875rem;
    font-size: 0.3125rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}
.clear-btn[data-v-1cf27b2a] {
    background: rgba(255, 107, 107, 0.8);
    color: white;
}
.clear-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 107, 107, 1);
}
.analysis-btn[data-v-1cf27b2a] {
    background: rgba(103, 126, 234, 0.8);
    color: white;
}
.analysis-btn[data-v-1cf27b2a]:hover {
    background: rgba(103, 126, 234, 1);
}
.rules-content[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 10rem; /* 与历史面板保持一致 */
    padding-right: 0.15625rem; /* 为滚动条留出空间 */
}
.rule-section[data-v-1cf27b2a] {
    margin-bottom: 0.3125rem;
}
.section-subtitle[data-v-1cf27b2a] {
    font-size: 0.375rem;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.95);
    display: block;
    margin-bottom: 0.1875rem;
    text-align: center;
}
.rule-list[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}
.rule-item-wrapper[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 0.125rem;
    padding: 0.125rem 0.09375rem;
    margin-bottom: 0.0625rem;
}
.rule-item[data-v-1cf27b2a] {
    font-size: 0.3125rem;
    font-weight: bold;
    display: block;
    margin-bottom: 0.03125rem;
    line-height: 1.2;
}
.rule-desc[data-v-1cf27b2a] {
    font-size: 0.25rem;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    margin-bottom: 0.03125rem;
    text-align: center;
    line-height: 1.1;
}
.rule-reward[data-v-1cf27b2a] {
    font-size: 0.28125rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    display: block;
    text-align: center;
    line-height: 1.1;
}
.rule-extra[data-v-1cf27b2a] {
    font-size: 0.25rem;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    text-align: center;
    margin-bottom: 0.0625rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.09375rem;
    border-radius: 0.0625rem;
    line-height: 1.2;
}

  /* 平板端滚动条样式 - 规则面板 */
.rules-content[data-v-1cf27b2a]::-webkit-scrollbar {
    width: 0.125rem;
}
.rules-content[data-v-1cf27b2a]::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.0625rem;
}
.rules-content[data-v-1cf27b2a]::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0.0625rem;
}
.rules-content[data-v-1cf27b2a]::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

  /* 平板端游戏规则字体颜色 - 优化协调性 */
.rule-sanhua[data-v-1cf27b2a] {
    color: #29B6F6 !important;
    /* 散花 - 中等蓝色，与豹子粉色形成明显区分 */
}
.rule-duizi[data-v-1cf27b2a] {
    color: #A5D6A7 !important;
    /* 对子 - 中等绿色，保持清新感 */
}
.rule-shunzi[data-v-1cf27b2a] {
    color: #FFCC02 !important;
    /* 顺子 - 金黄色，更加醒目 */
}
.rule-baozi[data-v-1cf27b2a] {
    color: #F8BBD9 !important;
    /* 豹子 - 浅粉色，温和而突出 */
}

  /* 中：游戏区域 */
.game-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.08);
    grid-area: game;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.46875rem;
    border-radius: 0.25rem;
    box-sizing: border-box;
    height: auto; /* 自动高度 */
    min-height: 9.375rem; /* 最小高度300rpx */
    max-height: 12.5rem; /* 最大高度400rpx */
    overflow: hidden; /* 防止内容溢出 */
}
.dice-container[data-v-1cf27b2a] {
    display: flex;
    gap: 0.46875rem;
    margin-bottom: 0.625rem;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem; /* 平板端骰子容器高度 */
    max-height: 2.5rem; /* 限制最大高度 */
}

  /* 平板端确保骰子之间有间距 */
.dice-container > *[data-v-1cf27b2a] {
    margin: 0 0.5625rem;
}
.dice-container > *[data-v-1cf27b2a]:first-child {
    margin-left: 0;
}
.dice-container > *[data-v-1cf27b2a]:last-child {
    margin-right: 0;
}
.game-result[data-v-1cf27b2a] {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.46875rem;
    border-radius: 0.25rem;
    width: 85%; /* 调整为85%宽度 */
    max-width: 12.5rem; /* 限制最大宽度 */
    margin: 0.46875rem auto 0; /* 居中显示 */
    min-height: 1.25rem; /* 调整最小高度 */
    max-height: 1.875rem; /* 调整最大高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* 防止内容溢出 */
    box-shadow: 0 0.09375rem 0.25rem rgba(0, 0, 0, 0.12); /* 添加阴影效果 */
}
.result-content[data-v-1cf27b2a] {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 0.9375rem; /* 平板端内容区域固定高度 */
    overflow: hidden; /* 防止内容溢出影响布局 */
}
.result-prompt[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    line-height: 1.1;
}
.result-rolling[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    font-weight: bold;
    color: #ffa500;
    display: block;
    line-height: 1.1;
    animation: rolling-pulse-1cf27b2a 1s ease-in-out infinite;
}
.dice-numbers[data-v-1cf27b2a] {
    font-size: 0.4375rem;
    font-weight: bold;
    color: #333;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.03125rem 0.1875rem;
    border-radius: 0.25rem;
    display: inline-block;
    margin-bottom: 0.0625rem;
}
.result-type[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    margin-bottom: 0.1875rem;
}

  /* 平板端游戏结果类型颜色 - 与游戏规则保持一致 */
.result-sanhua[data-v-1cf27b2a] {
    color: #29B6F6 !important; /* 散花 - 中等蓝色 */
}
.result-duizi[data-v-1cf27b2a] {
    color: #A5D6A7 !important; /* 对子 - 中等绿色 */
}
.result-shunzi[data-v-1cf27b2a] {
    color: #FFCC02 !important; /* 顺子 - 金黄色 */
}
.result-baozi[data-v-1cf27b2a] {
    color: #F8BBD9 !important; /* 豹子 - 浅粉色 */
}
.result-weight[data-v-1cf27b2a],
  .result-beer[data-v-1cf27b2a] {
    font-size: 0.4375rem;
    color: #666;
    display: block;
    margin-bottom: 0.125rem;
}
.result-profit[data-v-1cf27b2a] {
    font-size: 0.5rem;
    font-weight: bold;
    color: #27ae60;
    display: block;
}

  /* 右：游戏记录面板 */
.history-panel[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: history;
    padding: 0.3125rem;
    border-radius: 0.25rem;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: auto; /* 改为自动高度 */
    min-height: 9.375rem; /* 设置最小高度 */
    max-height: 12.5rem; /* 限制最大高度，与游戏区域一致 */
}
.history-content[data-v-1cf27b2a] {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 10rem; /* 固定最大高度，确保可滚动 */
    min-height: 6.25rem; /* 最小高度 */
    padding-right: 0.15625rem; /* 为滚动条留出空间 */
}

  /* 平板端滚动条样式 - 历史面板 */
.history-content[data-v-1cf27b2a]::-webkit-scrollbar {
    width: 0.125rem;
}
.history-content[data-v-1cf27b2a]::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.0625rem;
}
.history-content[data-v-1cf27b2a]::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0.0625rem;
}
.history-content[data-v-1cf27b2a]::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
.no-history[data-v-1cf27b2a] {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.375rem;
    padding: 0.46875rem 0.15625rem;
}
.history-list[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    padding-right: 0.15625rem; /* 为滚动条留出空间 */
}
.history-item[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.09375rem 0.1875rem;
    border-radius: 0.09375rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.0625rem;
}
.history-dice-container[data-v-1cf27b2a] {
    display: flex;
    gap: 0.1875rem; /* 增加间隔，从2rpx改为6rpx */
    align-items: center;
}
.history-dice-single[data-v-1cf27b2a] {
    width: 0.375rem;
    height: 0.375rem;
    background: white;
    border-radius: 0.0625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.2);
    border: 0.01563rem solid rgba(0, 0, 0, 0.1);
}
.history-dice-number[data-v-1cf27b2a] {
    font-size: 0.21875rem;
    font-weight: bold;
    color: #333;
    line-height: 1;
}
.history-type[data-v-1cf27b2a] {
    font-size: 0.25rem;
    color: white;
    font-weight: bold;
    line-height: 1.2;
}

  /* 平板端游戏记录背景颜色 - 与新颜色方案协调 */
.history-sanhua[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #29B6F6 0%, #1976D2 100%) !important;
    /* 散花 - 蓝色渐变背景 */
}
.history-sanhua .history-type[data-v-1cf27b2a],
  .history-sanhua[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在紫色背景上 */
}
.history-duizi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #A5D6A7 0%, #66BB6A 100%) !important;
    /* 对子 - 绿色渐变背景 */
}
.history-duizi .history-dice[data-v-1cf27b2a],
  .history-duizi .history-type[data-v-1cf27b2a],
  .history-duizi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在绿色背景上 */
}
.history-shunzi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #FFCC02 0%, #FFA000 100%) !important;
    /* 顺子 - 金色渐变背景 */
}
.history-shunzi .history-dice[data-v-1cf27b2a],
  .history-shunzi .history-type[data-v-1cf27b2a],
  .history-shunzi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在金色背景上 */
}
.history-baozi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #F8BBD9 0%, #EC407A 100%) !important;
    /* 豹子 - 粉色渐变背景 */
}
.history-baozi .history-dice[data-v-1cf27b2a],
  .history-baozi .history-type[data-v-1cf27b2a],
  .history-baozi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在粉色背景上 */
}

  /* 下部：啤酒选择按钮 */
.bottom-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: bottom;
    position: relative;
    padding: 0.3125rem;
    border-radius: 0.3125rem;
    box-sizing: border-box;
}
.bottom-actions[data-v-1cf27b2a] {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    display: flex;
    gap: 0.125rem;
    z-index: 10;
}
.beer-buttons[data-v-1cf27b2a] {
    display: grid;
    grid-template-columns: 6fr 6fr 1fr 6fr 6fr;
    gap: 0;
    width: 100%;
}
.beer-btn[data-v-1cf27b2a] {
    padding: 0;
    border-radius: 0.46875rem;
    border: 0.0625rem solid rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.5625rem;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.875rem;
    box-sizing: border-box;
    transition: all 0.2s ease;
}
.btn-content[data-v-1cf27b2a] {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.1875rem;
    padding: 0.375rem 0.46875rem;
    height: 100%;
}
.btn-icon[data-v-1cf27b2a] {
    font-size: 0.625rem;
}
.btn-text[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.09375rem;
    height: 1.5625rem;
}
.btn-title[data-v-1cf27b2a] {
    font-size: 0.40625rem;
    font-weight: bold;
    color: white;
    line-height: 0.9375rem;
    margin-bottom: -0.03125rem;
}
.btn-price[data-v-1cf27b2a] {
    font-size: 0.34375rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: normal;
    line-height: 20 rpx;
}
.beer-buttons .draft-btn[data-v-1cf27b2a] {
    grid-column: 2;
}
.beer-buttons .fruit-btn[data-v-1cf27b2a] {
    grid-column: 4;
}
.beer-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.15);
}
.beer-btn.active[data-v-1cf27b2a] {
    border-color: rgba(255, 255, 255, 0.8);
}
.draft-btn.active[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}
.draft-btn.active .btn-title[data-v-1cf27b2a] {
    color: white;
}
.draft-btn.active .btn-price[data-v-1cf27b2a] {
    color: rgba(255, 255, 255, 0.9);
}
.fruit-btn.active[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}
.fruit-btn.active .btn-title[data-v-1cf27b2a] {
    color: white;
}
.fruit-btn.active .btn-price[data-v-1cf27b2a] {
    color: rgba(255, 255, 255, 0.9);
}

  /* 平板端按钮禁用状态 */
.beer-btn.disabled[data-v-1cf27b2a],
  .beer-btn[data-v-1cf27b2a]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
}
}

/* ================================= PC端样式 (1024px+) ================================= */
@media screen and (min-width: 1024px) {
.game-container[data-v-1cf27b2a] {
    min-height: 100vh;
    display: grid;
    grid-template-rows: auto 1fr auto;
    grid-template-areas:
      "header"
      "middle"
      "bottom";
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.25rem;
    gap: 1.25rem;
    max-width: 43.75rem;
    margin: 0 auto;
}

  /* 上部：标题栏 */
.header-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5625rem 1.875rem;
    border-radius: 0.78125rem;
}
.rules-btn[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 1.25rem;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
f

  .rules-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-0.09375rem);
}
.header-buttons[data-v-1cf27b2a] {
    display: flex;
    gap: 0.625rem;
    flex-shrink: 0;
}
.header-btn[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 0.0625rem solid rgba(255, 255, 255, 0.3);
    padding: 0.78125rem 1.25rem;
    border-radius: 0.9375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
}
.header-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-0.09375rem);
}
.title-section[data-v-1cf27b2a] {
    text-align: center;
    flex: 1;
}
.main-title[data-v-1cf27b2a] {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    display: block;
    margin-bottom: 0.5rem;
}
.sub-title[data-v-1cf27b2a] {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    display: block;
}

  /* 中部：三栏布局 */
.middle-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.05);
    grid-area: middle;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-areas: "rules game history";
    gap: 1.09375rem;
    padding: 1.25rem;
    border-radius: 0.78125rem;
    transition: grid-template-columns 0.3s ease;
    align-items: stretch;
}

  /* 隐藏规则面板时的布局 */
.middle-area.hide-rules[data-v-1cf27b2a] {
    grid-template-columns: 0 3fr 1fr;
    grid-template-areas: "game game history";
}

  /* 隐藏历史面板时的布局 */
.middle-area.hide-history[data-v-1cf27b2a] {
    grid-template-columns: 1fr 3fr 0;
    grid-template-areas: "rules game game";
}

  /* 隐藏两个面板时的布局 */
.middle-area.hide-both[data-v-1cf27b2a] {
    grid-template-columns: 0 1fr 0;
    grid-template-areas: "game game game";
}

  /* 左：游戏规则面板 */
.rules-panel[data-v-1cf27b2a] {
    
    grid-area: rules;
    padding: 1.25rem;
    border-radius: 0.625rem;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-height: 100%;
}
.panel-header[data-v-1cf27b2a] {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    margin-bottom: 0.78125rem;
}
.panel-title[data-v-1cf27b2a] {
    font-size: 1.125rem;
    font-weight: bold;
    color: white;
    display: block;
}
.record-count[data-v-1cf27b2a] {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    display: block;
}
.action-btn[data-v-1cf27b2a] {
    padding: 0.25rem 0.5rem;
    border-radius: 0.46875rem;
    font-size: 0.625rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}
.clear-btn[data-v-1cf27b2a] {
    background: rgba(255, 107, 107, 0.8);
    color: white;
}
.clear-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 107, 107, 1);
    transform: translateY(-0.0625rem);
}
.analysis-btn[data-v-1cf27b2a] {
    background: rgba(103, 126, 234, 0.8);
    color: white;
}
.analysis-btn[data-v-1cf27b2a]:hover {
    background: rgba(103, 126, 234, 1);
    transform: translateY(-0.0625rem);
}
.rules-content[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    flex: 1;
}
.rule-section[data-v-1cf27b2a] {
    margin-bottom: 0.625rem;
}
.section-subtitle[data-v-1cf27b2a] {
    font-size: 0.75rem;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.95);
    display: block;
    margin-bottom: 0.375rem;
    text-align: center;
}
.rule-list[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.3125rem;
}
.rule-item-wrapper[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 0.25rem;
    padding: 0.375rem 0.3125rem;
    margin-bottom: 0.1875rem;
}
.rule-item[data-v-1cf27b2a] {
    font-size: 0.6875rem;
    font-weight: bold;
    display: block;
    margin-bottom: 0.125rem;
    line-height: 1.3;
}
.rule-desc[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    margin-bottom: 0.125rem;
    text-align: center;
    line-height: 1.3;
}
.rule-reward[data-v-1cf27b2a] {
    font-size: 0.625rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    display: block;
    text-align: center;
    line-height: 1.3;
}
.rule-extra[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    text-align: center;
    margin-bottom: 0.25rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.25rem;
    border-radius: 0.1875rem;
    line-height: 1.4;
}

  /* PC端游戏规则字体颜色 - 大屏幕优化 */
.rule-sanhua[data-v-1cf27b2a] {
    color: #81D4FA !important;
    /* 散花 - 浅蓝色，与豹子粉色形成明显区分 */
}
.rule-duizi[data-v-1cf27b2a] {
    color: #C8E6C9 !important;
    /* 对子 - 浅绿色，清新自然 */
}
.rule-shunzi[data-v-1cf27b2a] {
    color: #FFE082 !important;
    /* 顺子 - 浅金色，温暖明亮 */
}
.rule-baozi[data-v-1cf27b2a] {
    color: #F8BBD9 !important;
    /* 豹子 - 浅粉色，优雅突出 */
}

  /* 中：游戏区域 */
.game-area[data-v-1cf27b2a] {
    
    grid-area: game;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5625rem;
    border-radius: 0.625rem;
    box-sizing: border-box;
    min-height: 100%;
}
.dice-container[data-v-1cf27b2a] {
    display: flex;
    gap: 1.25rem;
    margin-bottom: 1.5625rem;
    align-items: center;
    justify-content: center;
}

  /* PC端确保骰子之间有间距 */
.dice-container > *[data-v-1cf27b2a] {
    margin: 0 0.625rem;
}
.dice-container > *[data-v-1cf27b2a]:first-child {
    margin-left: 0;
}
.dice-container > *[data-v-1cf27b2a]:last-child {
    margin-right: 0;
}
.game-result[data-v-1cf27b2a] {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.9375rem;
    border-radius: 0.625rem;
    width: 80%; /* 调整为80%宽度 */
    max-width: 18.75rem; /* 限制最大宽度 */
    margin: 0.9375rem auto 0; /* 居中显示 */
    min-height: 3.125rem; /* PC端固定最小高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.15); /* 添加阴影效果 */
}
.result-content[data-v-1cf27b2a] {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 1.875rem; /* PC端内容区域固定高度 */
}
.result-prompt[data-v-1cf27b2a] {
    font-size: 1.125rem;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    line-height: 1.2;
}
.result-rolling[data-v-1cf27b2a] {
    font-size: 1.125rem;
    font-weight: bold;
    color: #ffa500;
    display: block;
    line-height: 1.2;
    animation: rolling-pulse-1cf27b2a 1s ease-in-out infinite;
}
.dice-numbers[data-v-1cf27b2a] {
    font-size: 0.875rem;
    font-weight: bold;
    color: #333;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.1875rem 0.5rem;
    border-radius: 0.625rem;
    display: inline-block;
}
.result-type[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    margin-bottom: 0.0625rem;
}

  /* PC端游戏结果类型颜色 - 与游戏规则保持一致 */
.result-sanhua[data-v-1cf27b2a] {
    color: #81D4FA !important; /* 散花 - 浅蓝色 */
}
.result-duizi[data-v-1cf27b2a] {
    color: #C8E6C9 !important; /* 对子 - 浅绿色 */
}
.result-shunzi[data-v-1cf27b2a] {
    color: #FFE082 !important; /* 顺子 - 浅金色 */
}
.result-baozi[data-v-1cf27b2a] {
    color: #F8BBD9 !important; /* 豹子 - 浅粉色 */
}
.result-weight[data-v-1cf27b2a],
  .result-beer[data-v-1cf27b2a] {
    font-size: 1rem;
    color: #666;
    display: block;
    margin-bottom: 0.375rem;
}
.result-profit[data-v-1cf27b2a] {
    font-size: 1.125rem;
    font-weight: bold;
    color: #27ae60;
    display: block;
}

  /* 右：游戏记录面板 */
.history-panel[data-v-1cf27b2a] {
  
    grid-area: history;
    padding: 1.25rem;
    border-radius: 0.625rem;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-height: 100%;
}
.history-content[data-v-1cf27b2a] {
    flex: 1;
    overflow-y: auto;
}
.no-history[data-v-1cf27b2a] {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9375rem;
    padding: 1.25rem;
}
.history-list[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    gap: 0.46875rem;
}
.history-item[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}
.history-dice-container[data-v-1cf27b2a] {
    display: flex;
    gap: 0.1875rem;
    align-items: center;
}
.history-dice-single[data-v-1cf27b2a] {
    width: 1rem;
    height: 1rem;
    background: white;
    border-radius: 0.1875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.09375rem 0.1875rem rgba(0, 0, 0, 0.2);
    border: 0.03125rem solid rgba(0, 0, 0, 0.1);
}
.history-dice-number[data-v-1cf27b2a] {
    font-size: 0.5625rem;
    font-weight: bold;
    color: #333;
    line-height: 1;
}
.history-type[data-v-1cf27b2a] {
    font-size: 0.75rem;
    color: white;
    font-weight: bold;
}

  /* PC端游戏记录背景颜色 - 与新颜色方案协调 */
.history-sanhua[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #81D4FA 0%, #42A5F5 100%) !important;
    /* 散花 - 浅蓝色渐变背景 */
}
.history-sanhua .history-type[data-v-1cf27b2a],
  .history-sanhua[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在紫色背景上 */
}
.history-duizi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #C8E6C9 0%, #81C784 100%) !important;
    /* 对子 - 浅绿色渐变背景 */
}
.history-duizi .history-dice[data-v-1cf27b2a],
  .history-duizi .history-type[data-v-1cf27b2a],
  .history-duizi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在绿色背景上 */
}
.history-shunzi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #FFE082 0%, #FFB300 100%) !important;
    /* 顺子 - 浅金色渐变背景 */
}
.history-shunzi .history-dice[data-v-1cf27b2a],
  .history-shunzi .history-type[data-v-1cf27b2a],
  .history-shunzi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在金色背景上 */
}
.history-baozi[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #F8BBD9 0%, #F06292 100%) !important;
    /* 豹子 - 浅粉色渐变背景 */
}
.history-baozi .history-dice[data-v-1cf27b2a],
  .history-baozi .history-type[data-v-1cf27b2a],
  .history-baozi[data-v-1cf27b2a]  {
    color: white !important;
    /* 白色字体在粉色背景上 */
}

  /* 下部：啤酒选择按钮 */
.bottom-area[data-v-1cf27b2a] {
    background: rgba(255, 255, 255, 0.1);
    grid-area: bottom;
    position: relative;
    padding: 1.40625rem;
    border-radius: 0.78125rem;
}
.bottom-actions[data-v-1cf27b2a] {
    position: absolute;
    top: 0.625rem;
    right: 0.625rem;
    display: flex;
    gap: 0.375rem;
    z-index: 10;
}
.beer-buttons[data-v-1cf27b2a] {
    display: grid;
    grid-template-columns: 1fr 3fr 2fr 3fr 1fr;
    gap: 0;
    width: 100%;
}
.beer-btn[data-v-1cf27b2a] {
    padding: 0;
    border-radius: 0.78125rem;
    border: 0.09375rem solid rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1.125rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}
.btn-content[data-v-1cf27b2a] {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.46875rem;
    padding: 1.40625rem 1.09375rem;
    height: 100%;
}
.btn-icon[data-v-1cf27b2a] {
    font-size: 1.5rem;
}
.btn-text[data-v-1cf27b2a] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
    line-height: 1;
}
.btn-title[data-v-1cf27b2a] {
    font-size: 0.875rem;
    font-weight: bold;
    color: white;
    line-height: 1;
    margin-bottom: -0.0625rem;
}
.btn-price[data-v-1cf27b2a] {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: normal;
    line-height: 1;
}
.beer-buttons .draft-btn[data-v-1cf27b2a] {
    grid-column: 2;
}
.beer-buttons .fruit-btn[data-v-1cf27b2a] {
    grid-column: 4;
}
.beer-btn[data-v-1cf27b2a]:hover {
    background: rgba(255, 255, 255, 0.15);
}
.beer-btn.active[data-v-1cf27b2a] {
    border-color: rgba(255, 255, 255, 0.8);
}
.draft-btn.active[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}
.draft-btn.active .btn-title[data-v-1cf27b2a] {
    color: white;
}
.draft-btn.active .btn-price[data-v-1cf27b2a] {
    color: rgba(255, 255, 255, 0.9);
}
.fruit-btn.active[data-v-1cf27b2a] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}
.fruit-btn.active .btn-title[data-v-1cf27b2a] {
    color: white;
}
.fruit-btn.active .btn-price[data-v-1cf27b2a] {
    color: rgba(255, 255, 255, 0.9);
}

  /* PC端按钮禁用状态 */
.beer-btn.disabled[data-v-1cf27b2a],
  .beer-btn[data-v-1cf27b2a]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
}
}}

/* 中大奖动画样式 */
.jackpot-overlay[data-v-1cf27b2a] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: jackpotFadeIn-1cf27b2a 0.5s ease-out;
}
.jackpot-container[data-v-1cf27b2a] {
  position: relative;
  text-align: center;
  animation: jackpotBounce-1cf27b2a 0.8s ease-out;
}
.jackpot-content[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  padding: 1.875rem 1.25rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.625rem 1.875rem rgba(255, 215, 0, 0.5);
  border: 0.1875rem solid #FFD700;
  position: relative;
  overflow: hidden;
}
.jackpot-content[data-v-1cf27b2a]::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: jackpotShine-1cf27b2a 2s infinite;
}
.jackpot-title[data-v-1cf27b2a] {
  font-size: 1.5rem;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 0.625rem;
  text-shadow: 0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
  animation: jackpotPulse-1cf27b2a 1s infinite alternate;
}
.jackpot-subtitle[data-v-1cf27b2a] {
  font-size: 1rem;
  color: #8B4513;
  display: block;
  margin-bottom: 0.9375rem;
  font-weight: bold;
}
.jackpot-dice[data-v-1cf27b2a] {
  display: flex;
  justify-content: center;
  gap: 0.625rem;
  margin-bottom: 0.9375rem;
}
.jackpot-dice-item[data-v-1cf27b2a] {
  width: 2.5rem;
  height: 2.5rem;
  background: white;
  border-radius: 0.46875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.3);
  animation: jackpotDiceRotate-1cf27b2a 1s infinite linear;
}
.jackpot-dice-number[data-v-1cf27b2a] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.jackpot-reward[data-v-1cf27b2a] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #8B4513;
  display: block;
  text-shadow: 0.03125rem 0.03125rem 0.0625rem rgba(0, 0, 0, 0.3);
}

/* 烟花效果 */
.jackpot-fireworks[data-v-1cf27b2a] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.firework[data-v-1cf27b2a] {
  position: absolute;
  width: 0.3125rem;
  height: 0.3125rem;
  border-radius: 50%;
  animation: fireworkExplode-1cf27b2a 2s infinite;
}
.firework-1[data-v-1cf27b2a] {
  top: 20%;
  left: 20%;
  background: #FF6B6B;
  animation-delay: 0s;
}
.firework-2[data-v-1cf27b2a] {
  top: 30%;
  right: 20%;
  background: #4ECDC4;
  animation-delay: 0.5s;
}
.firework-3[data-v-1cf27b2a] {
  bottom: 30%;
  left: 30%;
  background: #45B7D1;
  animation-delay: 1s;
}
.firework-4[data-v-1cf27b2a] {
  bottom: 20%;
  right: 30%;
  background: #96CEB4;
  animation-delay: 1.5s;
}

/* 闪烁星星效果 */
.jackpot-sparkles[data-v-1cf27b2a] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.sparkle[data-v-1cf27b2a] {
  position: absolute;
  font-size: 0.9375rem;
  animation: sparkleFloat-1cf27b2a 3s infinite ease-in-out;
}
.sparkle-1[data-v-1cf27b2a] {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}
.sparkle-2[data-v-1cf27b2a] {
  top: 15%;
  right: 15%;
  animation-delay: 0.5s;
}
.sparkle-3[data-v-1cf27b2a] {
  top: 50%;
  left: 5%;
  animation-delay: 1s;
}
.sparkle-4[data-v-1cf27b2a] {
  top: 50%;
  right: 5%;
  animation-delay: 1.5s;
}
.sparkle-5[data-v-1cf27b2a] {
  bottom: 15%;
  left: 15%;
  animation-delay: 2s;
}
.sparkle-6[data-v-1cf27b2a] {
  bottom: 10%;
  right: 10%;
  animation-delay: 2.5s;
}

/* 动画关键帧 */
@keyframes jackpotFadeIn-1cf27b2a {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes jackpotBounce-1cf27b2a {
0% {
    transform: scale(0.3) translateY(-3.125rem);
    opacity: 0;
}
50% {
    transform: scale(1.1) translateY(0);
    opacity: 1;
}
100% {
    transform: scale(1) translateY(0);
    opacity: 1;
}
}
@keyframes jackpotPulse-1cf27b2a {
0% {
    transform: scale(1);
}
100% {
    transform: scale(1.1);
}
}
@keyframes jackpotShine-1cf27b2a {
0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
}
100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
}
}
@keyframes jackpotDiceRotate-1cf27b2a {
0% {
    transform: rotateY(0deg);
}
100% {
    transform: rotateY(360deg);
}
}
@keyframes fireworkExplode-1cf27b2a {
0% {
    transform: scale(0);
    opacity: 1;
}
50% {
    transform: scale(3);
    opacity: 0.8;
}
100% {
    transform: scale(6);
    opacity: 0;
}
}
@keyframes sparkleFloat-1cf27b2a {
0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
}
50% {
    transform: translateY(-0.625rem) rotate(180deg);
    opacity: 1;
}
}