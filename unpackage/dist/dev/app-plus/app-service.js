if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global2 = uni.requireGlobal();
  ArrayBuffer = global2.ArrayBuffer;
  Int8Array = global2.Int8Array;
  Uint8Array = global2.Uint8Array;
  Uint8ClampedArray = global2.Uint8ClampedArray;
  Int16Array = global2.Int16Array;
  Uint16Array = global2.Uint16Array;
  Int32Array = global2.Int32Array;
  Uint32Array = global2.Uint32Array;
  Float32Array = global2.Float32Array;
  Float64Array = global2.Float64Array;
  BigInt64Array = global2.BigInt64Array;
  BigUint64Array = global2.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  const ON_LOAD = "onLoad";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const createLifeCycleHook = (lifecycle, flag = 0) => (hook, target = vue.getCurrentInstance()) => {
    !vue.isInSSRComponentSetup && vue.injectHook(lifecycle, hook, target);
  };
  const onLoad = /* @__PURE__ */ createLifeCycleHook(
    ON_LOAD,
    2
    /* HookFlags.PAGE */
  );
  const BeverageType = Object.freeze({
    YUAN_JIANG: "YUAN_JIANG",
    // 原浆
    GUO_PI: "GUO_PI",
    // 果啤
    // 枚举工具方法
    isValid(type) {
      return Object.values(this).includes(type);
    },
    getAll() {
      return [this.YUAN_JIANG, this.GUO_PI];
    },
    getName(type) {
      const names = {
        [this.YUAN_JIANG]: "原浆",
        [this.GUO_PI]: "果啤"
      };
      return names[type] || "未知";
    }
  });
  const BEER_CONFIG = {
    // 游戏费用 (元/次)
    gameFee: {
      [BeverageType.YUAN_JIANG]: 10,
      // 原浆 10元/次
      [BeverageType.GUO_PI]: 15
      // 果啤 15元/次
    },
    // 成本 (元/斤) - 根据您提供的实际成本
    costs: {
      [BeverageType.YUAN_JIANG]: 3.75,
      // 原浆成本 3.75元/斤
      [BeverageType.GUO_PI]: 6
      // 果啤成本 6元/斤
    },
    // 中文名称
    names: {
      [BeverageType.YUAN_JIANG]: "原浆",
      [BeverageType.GUO_PI]: "果啤"
    }
  };
  const DICE_TYPES = Object.freeze({
    SANHUA: { name: "散花", weight: 1 },
    // 三个不同点数
    DUIZI: { name: "对子", weight: 2 },
    // 两个相同点数
    SHUNZI: { name: "顺子", weight: 3 },
    // 连续三个点数
    BAOZI: { name: "豹子", weight: 4 },
    // 三个相同点数
    // 枚举工具方法
    getAll() {
      return [this.SANHUA, this.DUIZI, this.SHUNZI, this.BAOZI];
    },
    getByName(name) {
      return this.getAll().find((type) => type.name === name);
    },
    getWeight(name) {
      const type = this.getByName(name);
      return type ? type.weight : 1;
    }
  });
  class DiceCalculator {
    constructor() {
      this.history = this.loadHistory();
      this.cache = /* @__PURE__ */ new Map();
      this.maxHistorySize = 1e4;
    }
    /**
     * 生成三个随机点数 (1-6)
     */
    generateDice() {
      return [
        Math.floor(Math.random() * 6) + 1,
        Math.floor(Math.random() * 6) + 1,
        Math.floor(Math.random() * 6) + 1
      ];
    }
    /**
     * 判断点数类型
     */
    getDiceType(dice) {
      const sorted = [...dice].sort((a2, b2) => a2 - b2);
      const [a, b, c] = sorted;
      if (a === b && b === c) {
        return DICE_TYPES.BAOZI;
      }
      if (b === a + 1 && c === b + 1) {
        return DICE_TYPES.SHUNZI;
      }
      if (a === b || b === c || a === c) {
        return DICE_TYPES.DUIZI;
      }
      return DICE_TYPES.SANHUA;
    }
    /**
     * 计算啤酒重量
     */
    calculateBeerWeight(diceType) {
      return diceType.weight;
    }
    /**
     * 计算毛利率
     */
    calculateProfitMargin(revenue, cost) {
      if (revenue === 0)
        return 0;
      return (revenue - cost) / revenue * 100;
    }
    /**
     * 检查毛利率是否符合要求
     */
    isProfitMarginAcceptable(revenue, cost, minMargin = 40) {
      const margin = this.calculateProfitMargin(revenue, cost);
      return margin >= minMargin;
    }
    /**
     * 根据毛利率要求调整成本配置
     */
    adjustCostForProfitMargin(beerType, targetMargin = 40) {
      const gameFee = BEER_CONFIG.gameFee[beerType];
      const maxCostPerJin = gameFee * (1 - targetMargin / 100);
      return Math.max(maxCostPerJin, 1);
    }
    /**
     * 获取毛利率统计信息
     */
    getProfitMarginStats() {
      const stats = this.calculateTotalStats();
      const marginRanges = {
        excellent: 0,
        // >= 60%
        good: 0,
        // 40-59%
        warning: 0,
        // 20-39%
        poor: 0
        // < 20%
      };
      this.history.forEach((record) => {
        const revenue = record.游戏费用 || record.gameFee || 0;
        const cost = record.成本 || record.totalCost || 0;
        const margin = this.calculateProfitMargin(revenue, cost);
        if (margin >= 60)
          marginRanges.excellent++;
        else if (margin >= 40)
          marginRanges.good++;
        else if (margin >= 20)
          marginRanges.warning++;
        else
          marginRanges.poor++;
      });
      return {
        ranges: marginRanges,
        overall: stats.totalRevenue > 0 ? this.calculateProfitMargin(stats.totalRevenue, stats.totalCost) : 0,
        average: stats.totalGames > 0 ? stats.totalProfit / stats.totalRevenue * 100 : 0
      };
    }
    /**
     * 生成符合毛利率要求的骰子点数
     */
    generateDiceWithProfitControl(beerType = BeverageType.YUAN_JIANG) {
      const gameFee = BEER_CONFIG.gameFee[beerType];
      const costPerJin = BEER_CONFIG.costs[beerType];
      const minProfitMargin = 40;
      const maxAttempts = 100;
      let attempts = 0;
      let dice, diceType, weight, totalCost, profit, profitMargin, revenue;
      do {
        dice = this.generateDice();
        diceType = this.getDiceType(dice);
        weight = this.calculateBeerWeight(diceType);
        revenue = gameFee;
        totalCost = weight * costPerJin;
        profit = revenue - totalCost;
        profitMargin = this.calculateProfitMargin(revenue, totalCost);
        attempts++;
        if (profitMargin >= minProfitMargin || attempts >= maxAttempts) {
          break;
        }
      } while (true);
      return {
        dice,
        diceType,
        weight,
        revenue,
        totalCost,
        profit,
        profitMargin,
        attempts
      };
    }
    /**
     * 计算单次游戏结果（带毛利率控制）
     */
    calculateGameResult(beerType = BeverageType.YUAN_JIANG) {
      const gameFee = BEER_CONFIG.gameFee[beerType];
      const costPerJin = BEER_CONFIG.costs[beerType];
      const gameData = this.generateDiceWithProfitControl(beerType);
      const result = {
        dice: gameData.dice,
        diceType: gameData.diceType.name,
        weight: gameData.weight,
        beerType,
        beerTypeName: BEER_CONFIG.names[beerType],
        gameFee,
        costPerJin,
        revenue: gameData.revenue,
        totalCost: gameData.totalCost,
        profit: gameData.profit,
        profitMargin: Math.round(gameData.profitMargin * 100) / 100,
        // 保留2位小数
        attempts: gameData.attempts,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      this.addToHistory(result);
      return result;
    }
    /**
     * 添加到历史记录（按照指定格式）
     */
    addToHistory(result) {
      try {
        const record = {
          记录序列: this.history.length + 1,
          啤酒类型: result.beerTypeName || BEER_CONFIG.names[result.beerType],
          骰子点数: result.dice,
          点数类型: result.diceType,
          重量: result.weight,
          游戏费用: result.gameFee,
          成本: result.totalCost,
          盈利: result.profit,
          毛利率: result.profitMargin,
          时间戳: result.timestamp,
          // 保留原始数据用于兼容性
          _raw: result
        };
        this.history.push(record);
        this.limitHistorySize();
        this.clearCache();
        this.saveHistory();
      } catch (error) {
        formatAppLog("error", "at utils/diceCalculator.js:286", "添加历史记录失败:", error);
        throw error;
      }
    }
    /**
     * 获取历史记录
     */
    getHistory() {
      return this.history;
    }
    /**
     * 清空历史记录
     */
    clearHistory() {
      this.history = [];
      this.saveHistory();
    }
    /**
     * 计算总体统计（直接使用存储的数据，确保一致性）
     */
    calculateTotalStats(history = null) {
      const data = history || this.history;
      if (data.length === 0) {
        return {
          totalGames: 0,
          totalRevenue: 0,
          totalCost: 0,
          totalProfit: 0,
          totalWeight: 0,
          averageProfit: 0,
          averageProfitPerJin: 0,
          diceTypeStats: {
            "散花": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
            "对子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
            "顺子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
            "豹子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
          },
          beerTypeStats: {
            "原浆": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
            "果啤": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
          }
        };
      }
      const stats = {
        totalGames: data.length,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        totalWeight: 0,
        diceTypeStats: {
          "散花": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          "对子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          "顺子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          "豹子": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
        },
        beerTypeStats: {
          "原浆": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 },
          "果啤": { count: 0, totalRevenue: 0, totalCost: 0, totalProfit: 0, totalWeight: 0 }
        }
      };
      data.forEach((record) => {
        const beerType = record.啤酒类型 || record.beerTypeName || "原浆";
        const diceType = record.点数类型 || record.diceType || "散花";
        const weight = record.重量 || record.weight || 0;
        const revenue = record.游戏费用 || record.gameFee || record.revenue || 0;
        const cost = record.成本 || record.totalCost || 0;
        const profit = record.盈利 || record.profit || 0;
        stats.totalRevenue += revenue;
        stats.totalCost += cost;
        stats.totalProfit += profit;
        stats.totalWeight += weight;
        if (stats.diceTypeStats[diceType]) {
          stats.diceTypeStats[diceType].count++;
          stats.diceTypeStats[diceType].totalRevenue += revenue;
          stats.diceTypeStats[diceType].totalCost += cost;
          stats.diceTypeStats[diceType].totalProfit += profit;
          stats.diceTypeStats[diceType].totalWeight += weight;
        }
        if (stats.beerTypeStats[beerType]) {
          stats.beerTypeStats[beerType].count++;
          stats.beerTypeStats[beerType].totalRevenue += revenue;
          stats.beerTypeStats[beerType].totalCost += cost;
          stats.beerTypeStats[beerType].totalProfit += profit;
          stats.beerTypeStats[beerType].totalWeight += weight;
        }
      });
      stats.averageProfit = stats.totalGames > 0 ? stats.totalProfit / stats.totalGames : 0;
      stats.averageProfitPerJin = stats.totalWeight > 0 ? stats.totalProfit / stats.totalWeight : 0;
      return stats;
    }
    /**
     * 为AnalysisModal提供专门的统计数据（按照正确的业务逻辑）
     */
    getAnalysisData() {
      const stats = this.calculateTotalStats();
      return {
        // 第一行：总体统计
        overall: {
          totalRevenue: stats.totalRevenue,
          totalCost: stats.totalCost,
          totalProfit: stats.totalProfit,
          totalMargin: stats.totalRevenue > 0 ? stats.totalProfit / stats.totalRevenue * 100 : 0
        },
        // 第二行：啤酒类型统计
        beer: {
          draft: {
            revenue: stats.beerTypeStats["原浆"].totalRevenue,
            cost: stats.beerTypeStats["原浆"].totalCost,
            profit: stats.beerTypeStats["原浆"].totalProfit,
            margin: stats.beerTypeStats["原浆"].totalRevenue > 0 ? stats.beerTypeStats["原浆"].totalProfit / stats.beerTypeStats["原浆"].totalRevenue * 100 : 0
          },
          fruit: {
            revenue: stats.beerTypeStats["果啤"].totalRevenue,
            cost: stats.beerTypeStats["果啤"].totalCost,
            profit: stats.beerTypeStats["果啤"].totalProfit,
            margin: stats.beerTypeStats["果啤"].totalRevenue > 0 ? stats.beerTypeStats["果啤"].totalProfit / stats.beerTypeStats["果啤"].totalRevenue * 100 : 0
          }
        },
        // 第三行：点数类型统计
        dice: {
          散花: {
            revenue: stats.diceTypeStats["散花"].totalRevenue,
            cost: stats.diceTypeStats["散花"].totalCost,
            profit: stats.diceTypeStats["散花"].totalProfit,
            margin: stats.diceTypeStats["散花"].totalRevenue > 0 ? stats.diceTypeStats["散花"].totalProfit / stats.diceTypeStats["散花"].totalRevenue * 100 : 0
          },
          对子: {
            revenue: stats.diceTypeStats["对子"].totalRevenue,
            cost: stats.diceTypeStats["对子"].totalCost,
            profit: stats.diceTypeStats["对子"].totalProfit,
            margin: stats.diceTypeStats["对子"].totalRevenue > 0 ? stats.diceTypeStats["对子"].totalProfit / stats.diceTypeStats["对子"].totalRevenue * 100 : 0
          },
          顺子: {
            revenue: stats.diceTypeStats["顺子"].totalRevenue,
            cost: stats.diceTypeStats["顺子"].totalCost,
            profit: stats.diceTypeStats["顺子"].totalProfit,
            margin: stats.diceTypeStats["顺子"].totalRevenue > 0 ? stats.diceTypeStats["顺子"].totalProfit / stats.diceTypeStats["顺子"].totalRevenue * 100 : 0
          },
          豹子: {
            revenue: stats.diceTypeStats["豹子"].totalRevenue,
            cost: stats.diceTypeStats["豹子"].totalCost,
            profit: stats.diceTypeStats["豹子"].totalProfit,
            margin: stats.diceTypeStats["豹子"].totalRevenue > 0 ? stats.diceTypeStats["豹子"].totalProfit / stats.diceTypeStats["豹子"].totalRevenue * 100 : 0
          }
        },
        // 第四行：补充统计
        supplement: {
          totalGames: stats.totalGames,
          // 游戏总次数 = 记录集合的长度
          totalWeight: stats.totalWeight,
          // 总斤数 = 根据所有记录的点数算出的总斤数
          averageProfit: stats.averageProfit,
          // 平均每次盈利 = 总盈利 / 次数
          averageProfitPerJin: stats.averageProfitPerJin
          // 平均每斤盈利 = 总盈利 / 总斤数
        },
        // 原始统计数据
        raw: stats
      };
    }
    /**
     * 保存历史记录到本地存储
     */
    saveHistory() {
      if (typeof localStorage !== "undefined") {
        localStorage.setItem("dice_records", JSON.stringify(this.history));
      }
    }
    /**
     * 从本地存储加载历史记录
     */
    loadHistory() {
      if (typeof localStorage !== "undefined") {
        let saved = localStorage.getItem("dice_records");
        if (saved) {
          return JSON.parse(saved);
        }
        saved = localStorage.getItem("diceGameHistory");
        if (saved) {
          const oldData = JSON.parse(saved);
          localStorage.setItem("dice_records", saved);
          localStorage.removeItem("diceGameHistory");
          return oldData;
        }
      }
      return [];
    }
    /**
     * 迁移旧数据格式
     */
    migrateOldData(oldData) {
      this.history = oldData.map((item, index) => ({
        记录序列: index + 1,
        啤酒类型: item.beerTypeName || BEER_CONFIG.names[item.beerType] || "原浆",
        骰子点数: item.dice || [1, 1, 1],
        点数类型: item.diceType || "散花",
        重量: item.weight || 1,
        游戏费用: item.gameFee || 10,
        成本: item.totalCost || 0,
        盈利: item.profit || 0,
        毛利率: item.profitMargin || 0,
        时间戳: item.timestamp || (/* @__PURE__ */ new Date()).toISOString(),
        _raw: item
      }));
      this.saveHistory();
    }
    /**
     * 导出历史记录为 JSON
     */
    exportHistory() {
      return JSON.stringify(this.history, null, 2);
    }
    /**
     * 导入历史记录
     */
    importHistory(jsonData) {
      try {
        const data = JSON.parse(jsonData);
        if (Array.isArray(data)) {
          this.history = data;
          this.saveHistory();
          return true;
        }
      } catch (error) {
        formatAppLog("error", "at utils/diceCalculator.js:548", "导入历史记录失败:", error);
      }
      return false;
    }
    /**
     * 格式化显示数据的工具方法
     */
    formatDisplayData(record) {
      return {
        序列: record.记录序列 || "N/A",
        啤酒: record.啤酒类型 || "N/A",
        点数: Array.isArray(record.骰子点数) ? record.骰子点数.join("-") : "N/A",
        类型: record.点数类型 || "N/A",
        重量: `${record.重量 || 0}斤`,
        费用: `${record.游戏费用 || 0}元`,
        成本: `${record.成本 || 0}元`,
        盈利: `${record.盈利 || 0}元`,
        毛利率: `${record.毛利率 || 0}%`
      };
    }
    /**
     * 验证数据完整性
     */
    validateRecord(record) {
      const required = ["记录序列", "啤酒类型", "骰子点数", "点数类型", "重量"];
      const missing = required.filter((field) => !record.hasOwnProperty(field));
      return {
        isValid: missing.length === 0,
        missingFields: missing,
        record
      };
    }
    /**
     * 获取游戏配置信息
     */
    getGameConfig() {
      return {
        beerTypes: BeverageType,
        diceTypes: DICE_TYPES,
        beerConfig: BEER_CONFIG,
        minProfitMargin: 40
      };
    }
    /**
     * 清除缓存
     */
    clearCache() {
      this.cache.clear();
    }
    /**
     * 获取缓存的统计数据
     */
    getCachedStats() {
      const cacheKey = `stats_${this.history.length}_${Date.now()}`;
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }
      const stats = this.calculateTotalStats();
      this.cache.set(cacheKey, stats);
      if (this.cache.size > 100) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
      return stats;
    }
    /**
     * 性能监控
     */
    performanceMonitor(operation, fn) {
      const start = performance.now();
      const result = fn();
      const end = performance.now();
      if (end - start > 100) {
        formatAppLog("warn", "at utils/diceCalculator.js:634", `性能警告: ${operation} 耗时 ${(end - start).toFixed(2)}ms`);
      }
      return result;
    }
    /**
     * 数据完整性检查
     */
    validateDataIntegrity() {
      const issues = [];
      this.history.forEach((record, index) => {
        const validation = this.validateRecord(record);
        if (!validation.isValid) {
          issues.push({
            index,
            record,
            issues: validation.missingFields
          });
        }
      });
      return {
        isValid: issues.length === 0,
        issues,
        totalRecords: this.history.length
      };
    }
    /**
     * 自动清理过期数据
     */
    cleanupOldData(maxAge = 30 * 24 * 60 * 60 * 1e3) {
      const cutoffTime = new Date(Date.now() - maxAge);
      const originalLength = this.history.length;
      this.history = this.history.filter((record) => {
        const recordTime = new Date(record.时间戳 || record.timestamp);
        return recordTime > cutoffTime;
      });
      if (this.history.length !== originalLength) {
        this.saveHistory();
        this.clearCache();
        formatAppLog("log", "at utils/diceCalculator.js:679", `清理了 ${originalLength - this.history.length} 条过期记录`);
      }
    }
    /**
     * 限制历史记录大小
     */
    limitHistorySize() {
      if (this.history.length > this.maxHistorySize) {
        const excess = this.history.length - this.maxHistorySize;
        this.history = this.history.slice(excess);
        this.saveHistory();
        this.clearCache();
        formatAppLog("log", "at utils/diceCalculator.js:692", `清理了 ${excess} 条最旧的记录`);
      }
    }
    /**
     * 重置所有数据
     */
    reset() {
      this.clearHistory();
      this.clearCache();
      formatAppLog("log", "at utils/diceCalculator.js:702", "DiceCalculator已重置");
    }
    /**
     * 获取系统状态
     */
    getSystemStatus() {
      return {
        historyCount: this.history.length,
        cacheSize: this.cache.size,
        maxHistorySize: this.maxHistorySize,
        memoryUsage: this.history.length * 200,
        // 估算内存使用（字节）
        lastUpdate: this.history.length > 0 ? this.history[this.history.length - 1].时间戳 : null
      };
    }
  }
  const diceCalculator = new DiceCalculator();
  diceCalculator.debugTest = function() {
    formatAppLog("log", "at utils/diceCalculator.js:730", "=== DiceCalculator 调试测试 ===");
    formatAppLog("log", "at utils/diceCalculator.js:731", "历史记录数量:", this.history.length);
    formatAppLog("log", "at utils/diceCalculator.js:732", "历史记录:", this.history);
    const stats = this.calculateTotalStats();
    formatAppLog("log", "at utils/diceCalculator.js:735", "统计数据:", stats);
    const analysisData = this.getAnalysisData();
    formatAppLog("log", "at utils/diceCalculator.js:738", "分析数据:", analysisData);
    return {
      historyCount: this.history.length,
      stats,
      analysisData
    };
  };
  const formatNumber = (value) => {
    const num = Number(value);
    if (!isFinite(num))
      return "0";
    if (Number.isInteger(num)) {
      return num.toString();
    }
    return num.toFixed(2);
  };
  const formatCurrency = (value) => {
    const num = Number(value);
    if (!isFinite(num))
      return "0.00";
    return num.toFixed(2);
  };
  const formatPercentage = (value) => {
    const num = Number(value);
    if (!isFinite(num))
      return "0.00";
    return num.toFixed(2);
  };
  const formatInteger = (value) => {
    const num = Number(value);
    if (!isFinite(num))
      return "0";
    return Math.round(num).toString();
  };
  const calcTotalRevenue = () => {
    const history = diceCalculator.loadHistory();
    return history.reduce((sum, record) => {
      return sum + (record.游戏费用 || record.gameFee || record.revenue || 0);
    }, 0);
  };
  const calcTotalCost = () => {
    const history = diceCalculator.loadHistory();
    return history.reduce((sum, record) => {
      return sum + (record.成本 || record.totalCost || 0);
    }, 0);
  };
  const calcTotalProfit = () => {
    const history = diceCalculator.loadHistory();
    return history.reduce((sum, record) => {
      return sum + (record.盈利 || record.profit || 0);
    }, 0);
  };
  const calcTotalMargin = () => {
    const revenue = calcTotalRevenue();
    const cost = calcTotalCost();
    if (revenue === 0)
      return 0;
    return (revenue - cost) / revenue * 100;
  };
  const calcBeerTypeRevenue = (beerType) => {
    const history = diceCalculator.loadHistory();
    const typeName = BeverageType.getName(beerType);
    return history.filter((record) => (record.啤酒类型 || record.beerTypeName) === typeName).reduce((sum, record) => sum + (record.游戏费用 || record.gameFee || record.revenue || 0), 0);
  };
  const calcBeerTypeCost = (beerType) => {
    const history = diceCalculator.loadHistory();
    const typeName = BeverageType.getName(beerType);
    return history.filter((record) => (record.啤酒类型 || record.beerTypeName) === typeName).reduce((sum, record) => sum + (record.成本 || record.totalCost || 0), 0);
  };
  const calcBeerTypeProfit = (beerType) => {
    const history = diceCalculator.loadHistory();
    const typeName = BeverageType.getName(beerType);
    return history.filter((record) => (record.啤酒类型 || record.beerTypeName) === typeName).reduce((sum, record) => sum + (record.盈利 || record.profit || 0), 0);
  };
  const calcBeerTypeMargin = (beerType) => {
    const revenue = calcBeerTypeRevenue(beerType);
    const cost = calcBeerTypeCost(beerType);
    if (revenue === 0)
      return 0;
    return (revenue - cost) / revenue * 100;
  };
  const calcDiceTypeRevenue = (diceType) => {
    const history = diceCalculator.loadHistory();
    return history.filter((record) => (record.点数类型 || record.diceType) === diceType).reduce((sum, record) => sum + (record.游戏费用 || record.gameFee || record.revenue || 0), 0);
  };
  const calcDiceTypeCost = (diceType) => {
    const history = diceCalculator.loadHistory();
    return history.filter((record) => (record.点数类型 || record.diceType) === diceType).reduce((sum, record) => sum + (record.成本 || record.totalCost || 0), 0);
  };
  const calcDiceTypeProfit = (diceType) => {
    const history = diceCalculator.loadHistory();
    return history.filter((record) => (record.点数类型 || record.diceType) === diceType).reduce((sum, record) => sum + (record.盈利 || record.profit || 0), 0);
  };
  const calcDiceTypeMargin = (diceType) => {
    const revenue = calcDiceTypeRevenue(diceType);
    const cost = calcDiceTypeCost(diceType);
    if (revenue === 0)
      return 0;
    return (revenue - cost) / revenue * 100;
  };
  const calcTotalGames = () => {
    const history = diceCalculator.loadHistory();
    return history.length;
  };
  const calcTotalWeight = () => {
    const history = diceCalculator.loadHistory();
    return history.reduce((sum, record) => {
      return sum + (record.重量 || record.weight || 0);
    }, 0);
  };
  const calcAverageProfit = () => {
    const totalProfit = calcTotalProfit();
    const totalGames = calcTotalGames();
    return totalGames > 0 ? totalProfit / totalGames : 0;
  };
  const calcAverageProfitPerJin = () => {
    const totalProfit = calcTotalProfit();
    const totalWeight = calcTotalWeight();
    return totalWeight > 0 ? totalProfit / totalWeight : 0;
  };
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$4 = {
    __name: "RulesModal",
    props: {
      visible: {
        type: Boolean,
        default: false
      }
    },
    emits: ["close"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit = __emit;
      const closeModal = () => {
        emit("close");
      };
      const __returned__ = { props, emit, closeModal };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return $props.visible ? (vue.openBlock(), vue.createElementBlock("view", {
      key: 0,
      class: "modal-overlay",
      onClick: $setup.closeModal
    }, [
      vue.createElementVNode("view", {
        class: "modal-container",
        onClick: _cache[0] || (_cache[0] = vue.withModifiers(() => {
        }, ["stop"]))
      }, [
        vue.createCommentVNode(" 标题栏 "),
        vue.createElementVNode("view", { class: "modal-header" }, [
          vue.createElementVNode("text", { class: "modal-title" }, "🎲 游戏规则"),
          vue.createElementVNode("button", {
            class: "close-btn",
            onClick: $setup.closeModal
          }, "✕")
        ]),
        vue.createCommentVNode(" 规则内容 "),
        vue.createElementVNode("scroll-view", {
          class: "modal-content",
          "scroll-y": "true"
        }, [
          vue.createElementVNode("view", { class: "rule-section" }, [
            vue.createElementVNode("text", { class: "section-title" }, "🎲 基本玩法"),
            vue.createElementVNode("text", { class: "rule-text" }, "选择啤酒类型，支付游戏费用，摇骰子获得相应重量的啤酒")
          ]),
          vue.createElementVNode("view", { class: "rule-section" }, [
            vue.createElementVNode("text", { class: "section-title" }, "🍺 啤酒类型"),
            vue.createElementVNode("view", { class: "beer-info" }, [
              vue.createElementVNode("text", { class: "beer-item" }, "原浆：游戏费用 ¥10/次"),
              vue.createElementVNode("text", { class: "beer-item" }, "果啤：游戏费用 ¥15/次")
            ])
          ]),
          vue.createElementVNode("view", { class: "rule-section" }, [
            vue.createElementVNode("text", { class: "section-title" }, "🎯 点数类型与奖励"),
            vue.createElementVNode("view", { class: "dice-types" }, [
              vue.createElementVNode("view", { class: "type-item" }, [
                vue.createElementVNode("text", { class: "type-name" }, "散花"),
                vue.createElementVNode("text", { class: "type-desc" }, "三个不同点数"),
                vue.createElementVNode("text", { class: "type-reward" }, "奖励：1斤啤酒")
              ]),
              vue.createElementVNode("view", { class: "type-item" }, [
                vue.createElementVNode("text", { class: "type-name" }, "对子"),
                vue.createElementVNode("text", { class: "type-desc" }, "两个相同点数"),
                vue.createElementVNode("text", { class: "type-reward" }, "奖励：2斤啤酒")
              ]),
              vue.createElementVNode("view", { class: "type-item" }, [
                vue.createElementVNode("text", { class: "type-name" }, "顺子"),
                vue.createElementVNode("text", { class: "type-desc" }, "连续三个点数"),
                vue.createElementVNode("text", { class: "type-reward" }, "奖励：3斤啤酒")
              ]),
              vue.createElementVNode("view", { class: "type-item" }, [
                vue.createElementVNode("text", { class: "type-name" }, "豹子"),
                vue.createElementVNode("text", { class: "type-desc" }, "三个相同点数"),
                vue.createElementVNode("text", { class: "type-reward" }, "奖励：4斤啤酒")
              ])
            ])
          ]),
          vue.createElementVNode("view", { class: "rule-section" }, [
            vue.createElementVNode("text", { class: "section-title" }, "💰 盈利计算"),
            vue.createElementVNode("text", { class: "rule-text" }, "收入 = 游戏费用（固定）"),
            vue.createElementVNode("text", { class: "rule-text" }, "成本 = 奖励重量 × 每斤成本"),
            vue.createElementVNode("text", { class: "rule-text" }, "盈利 = 收入 - 成本"),
            vue.createElementVNode("view", { class: "example" }, [
              vue.createElementVNode("text", { class: "example-title" }, "示例："),
              vue.createElementVNode("text", { class: "example-text" }, "客户选择原浆，摇出对子"),
              vue.createElementVNode("text", { class: "example-text" }, "收入：¥10（游戏费用）"),
              vue.createElementVNode("text", { class: "example-text" }, "成本：2斤 × ¥3.75 = ¥7.5"),
              vue.createElementVNode("text", { class: "example-text" }, "盈利：¥10 - ¥7.5 = ¥2.5")
            ])
          ])
        ])
      ])
    ])) : vue.createCommentVNode("v-if", true);
  }
  const RulesModal = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__scopeId", "data-v-e3a2cddd"], ["__file", "/Users/<USER>/02-workspace/08_HbuilderSpace/top.resty.dice_game/components/RulesModal.vue"]]);
  const _sfc_main$3 = {
    __name: "AnalysisModal",
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      fullscreen: {
        type: Boolean,
        default: true
      }
    },
    emits: ["close"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit = __emit;
      const handleOverlayClick = () => {
        emit("close");
      };
      const refreshTrigger = vue.ref(0);
      vue.watch(() => props.visible, (newVisible) => {
        if (newVisible) {
          refreshTrigger.value++;
        }
      });
      const totalRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalRevenue();
      });
      const totalCost = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalCost();
      });
      const totalProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalProfit();
      });
      const totalMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalMargin();
      });
      const yuanJiangRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeRevenue(BeverageType.YUAN_JIANG);
      });
      const yuanJiangCost = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeCost(BeverageType.YUAN_JIANG);
      });
      const yuanJiangProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeProfit(BeverageType.YUAN_JIANG);
      });
      const yuanJiangMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeMargin(BeverageType.YUAN_JIANG);
      });
      const guoPiRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeRevenue(BeverageType.GUO_PI);
      });
      const guoPiCost = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeCost(BeverageType.GUO_PI);
      });
      const guoPiProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeProfit(BeverageType.GUO_PI);
      });
      const guoPiMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcBeerTypeMargin(BeverageType.GUO_PI);
      });
      const sanHuaRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeRevenue("散花");
      });
      const sanHuaCost = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeCost("散花");
      });
      const sanHuaProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeProfit("散花");
      });
      const sanHuaMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeMargin("散花");
      });
      const duiZiRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeRevenue("对子");
      });
      const duiZiCost = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeCost("对子");
      });
      const duiZiProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeProfit("对子");
      });
      const duiZiMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeMargin("对子");
      });
      const shunZiRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeRevenue("顺子");
      });
      const shunZiCost = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeCost("顺子");
      });
      const shunZiProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeProfit("顺子");
      });
      const shunZiMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeMargin("顺子");
      });
      const baoZiRevenue = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeRevenue("豹子");
      });
      const baoZiCost = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeCost("豹子");
      });
      const baoZiProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeProfit("豹子");
      });
      const baoZiMargin = vue.computed(() => {
        refreshTrigger.value;
        return calcDiceTypeMargin("豹子");
      });
      const totalGames = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalGames();
      });
      const totalWeight = vue.computed(() => {
        refreshTrigger.value;
        return calcTotalWeight();
      });
      const averageProfit = vue.computed(() => {
        refreshTrigger.value;
        return calcAverageProfit();
      });
      const averageProfitPerJin = vue.computed(() => {
        refreshTrigger.value;
        return calcAverageProfitPerJin();
      });
      const __returned__ = { props, emit, handleOverlayClick, refreshTrigger, totalRevenue, totalCost, totalProfit, totalMargin, yuanJiangRevenue, yuanJiangCost, yuanJiangProfit, yuanJiangMargin, guoPiRevenue, guoPiCost, guoPiProfit, guoPiMargin, sanHuaRevenue, sanHuaCost, sanHuaProfit, sanHuaMargin, duiZiRevenue, duiZiCost, duiZiProfit, duiZiMargin, shunZiRevenue, shunZiCost, shunZiProfit, shunZiMargin, baoZiRevenue, baoZiCost, baoZiProfit, baoZiMargin, totalGames, totalWeight, averageProfit, averageProfitPerJin, ref: vue.ref, computed: vue.computed, watch: vue.watch, get BeverageType() {
        return BeverageType;
      }, get formatNumber() {
        return formatNumber;
      }, get formatCurrency() {
        return formatCurrency;
      }, get formatPercentage() {
        return formatPercentage;
      }, get formatInteger() {
        return formatInteger;
      }, get calcTotalRevenue() {
        return calcTotalRevenue;
      }, get calcTotalCost() {
        return calcTotalCost;
      }, get calcTotalProfit() {
        return calcTotalProfit;
      }, get calcTotalMargin() {
        return calcTotalMargin;
      }, get calcBeerTypeRevenue() {
        return calcBeerTypeRevenue;
      }, get calcBeerTypeCost() {
        return calcBeerTypeCost;
      }, get calcBeerTypeProfit() {
        return calcBeerTypeProfit;
      }, get calcBeerTypeMargin() {
        return calcBeerTypeMargin;
      }, get calcDiceTypeRevenue() {
        return calcDiceTypeRevenue;
      }, get calcDiceTypeCost() {
        return calcDiceTypeCost;
      }, get calcDiceTypeProfit() {
        return calcDiceTypeProfit;
      }, get calcDiceTypeMargin() {
        return calcDiceTypeMargin;
      }, get calcTotalGames() {
        return calcTotalGames;
      }, get calcTotalWeight() {
        return calcTotalWeight;
      }, get calcAverageProfit() {
        return calcAverageProfit;
      }, get calcAverageProfitPerJin() {
        return calcAverageProfitPerJin;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return $props.visible ? (vue.openBlock(), vue.createElementBlock(
      "view",
      {
        key: 0,
        class: vue.normalizeClass(["modal-overlay", { "fullscreen": $props.fullscreen }]),
        onClick: $setup.handleOverlayClick
      },
      [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["modal-container", { "fullscreen": $props.fullscreen }]),
            onClick: _cache[1] || (_cache[1] = vue.withModifiers(() => {
            }, ["stop"]))
          },
          [
            vue.createElementVNode("view", { class: "modal-header" }, [
              vue.createElementVNode("text", { class: "modal-title" }, "📊 记录分析"),
              vue.createElementVNode("button", {
                class: "close-btn",
                onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("close"))
              }, "✕")
            ]),
            vue.createElementVNode("view", { class: "modal-content" }, [
              vue.createElementVNode("view", { class: "analysis-content" }, [
                vue.createCommentVNode(" 第一行：总体统计 "),
                vue.createElementVNode("view", {
                  class: "stats-row",
                  "data-title": "📊 总体统计"
                }, [
                  vue.createElementVNode("view", { class: "stat-card" }, [
                    vue.createElementVNode("text", { class: "stat-label" }, "总收入"),
                    vue.createElementVNode(
                      "text",
                      { class: "stat-value revenue" },
                      vue.toDisplayString($setup.formatCurrency($setup.totalRevenue)),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "stat-card" }, [
                    vue.createElementVNode("text", { class: "stat-label" }, "总成本"),
                    vue.createElementVNode(
                      "text",
                      { class: "stat-value cost" },
                      vue.toDisplayString($setup.formatCurrency($setup.totalCost)),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "stat-card" }, [
                    vue.createElementVNode("text", { class: "stat-label" }, "总盈利"),
                    vue.createElementVNode(
                      "text",
                      {
                        class: vue.normalizeClass(["stat-value profit", { negative: $setup.totalProfit < 0 }])
                      },
                      vue.toDisplayString($setup.formatCurrency($setup.totalProfit)),
                      3
                      /* TEXT, CLASS */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "stat-card" }, [
                    vue.createElementVNode("text", { class: "stat-label" }, "毛润率"),
                    vue.createElementVNode(
                      "text",
                      {
                        class: vue.normalizeClass(["stat-value margin", {
                          good: $setup.totalMargin >= 40,
                          warning: $setup.totalMargin >= 20 && $setup.totalMargin < 40,
                          danger: $setup.totalMargin < 20
                        }])
                      },
                      vue.toDisplayString($setup.formatPercentage($setup.totalMargin)) + "% ",
                      3
                      /* TEXT, CLASS */
                    )
                  ])
                ]),
                vue.createCommentVNode(" 第二行：啤酒类型统计 "),
                vue.createElementVNode("view", {
                  class: "stats-row",
                  "data-title": "🍺 啤酒类型统计"
                }, [
                  vue.createCommentVNode(" 原浆啤酒卡片 "),
                  vue.createElementVNode("view", { class: "stat-card beer-draft" }, [
                    vue.createElementVNode("view", { class: "beer-stat-row" }, [
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "原浆收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.yuanJiangRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "原浆成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.yuanJiangCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "beer-stat-row" }, [
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "原浆盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.yuanJiangProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "原浆毛润"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.yuanJiangMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ]),
                  vue.createCommentVNode(" 果啤卡片 "),
                  vue.createElementVNode("view", { class: "stat-card beer-fruit" }, [
                    vue.createElementVNode("view", { class: "beer-stat-row" }, [
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "果啤收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.guoPiRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "果啤成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.guoPiCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "beer-stat-row" }, [
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "果啤盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.guoPiProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "beer-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "果啤毛润"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.guoPiMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ])
                ]),
                vue.createCommentVNode(" 第三行：点数类型统计 "),
                vue.createElementVNode("view", {
                  class: "stats-row",
                  "data-title": "🎲 点数类型统计"
                }, [
                  vue.createCommentVNode(" 散花卡片 "),
                  vue.createElementVNode("view", { class: "stat-card dice-sanhua" }, [
                    vue.createElementVNode("view", { class: "dice-type-title" }, "散花"),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.sanHuaRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.sanHuaCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.sanHuaProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "利润率"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.sanHuaMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ]),
                  vue.createCommentVNode(" 对子卡片 "),
                  vue.createElementVNode("view", { class: "stat-card dice-duizi" }, [
                    vue.createElementVNode("view", { class: "dice-type-title" }, "对子"),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.duiZiRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.duiZiCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.duiZiProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "利润率"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.duiZiMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ]),
                  vue.createCommentVNode(" 顺子卡片 "),
                  vue.createElementVNode("view", { class: "stat-card dice-shunzi" }, [
                    vue.createElementVNode("view", { class: "dice-type-title" }, "顺子"),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.shunZiRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.shunZiCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.shunZiProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "利润率"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.shunZiMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ]),
                  vue.createCommentVNode(" 豹子卡片 "),
                  vue.createElementVNode("view", { class: "stat-card dice-baozi" }, [
                    vue.createElementVNode("view", { class: "dice-type-title" }, "豹子"),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "收入"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.baoZiRevenue)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "成本"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.baoZiCost)),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createElementVNode("view", { class: "dice-stat-row" }, [
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.baoZiProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "dice-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "利润率"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatPercentage($setup.baoZiMargin)) + "%",
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ])
                ]),
                vue.createCommentVNode(" 第四行：补充统计 "),
                vue.createElementVNode("view", {
                  class: "stats-row",
                  "data-title": "📈 补充统计"
                }, [
                  vue.createElementVNode("view", { class: "stat-card" }, [
                    vue.createElementVNode("view", { class: "supplement-stat-row" }, [
                      vue.createElementVNode("view", { class: "supplement-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "游戏次数"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatInteger($setup.totalGames)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "supplement-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "平均盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.averageProfit)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "supplement-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "总重量"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatInteger($setup.totalWeight)),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createElementVNode("view", { class: "supplement-stat-item" }, [
                        vue.createElementVNode("text", { class: "stat-label" }, "平均每斤盈利"),
                        vue.createElementVNode(
                          "text",
                          { class: "stat-value" },
                          vue.toDisplayString($setup.formatCurrency($setup.averageProfitPerJin)),
                          1
                          /* TEXT */
                        )
                      ])
                    ])
                  ])
                ])
              ])
            ])
          ],
          2
          /* CLASS */
        )
      ],
      2
      /* CLASS */
    )) : vue.createCommentVNode("v-if", true);
  }
  const AnalysisModal = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__scopeId", "data-v-07be8c39"], ["__file", "/Users/<USER>/02-workspace/08_HbuilderSpace/top.resty.dice_game/components/AnalysisModal.vue"]]);
  const _sfc_main$2 = {
    __name: "dice",
    props: {
      value: {
        type: Number,
        default: 1,
        validator: (value) => value >= 1 && value <= 6
      },
      size: {
        type: Number,
        default: 80
        // 默认尺寸 80rpx
      },
      animated: {
        type: Boolean,
        default: false
      }
    },
    setup(__props, { expose: __expose }) {
      const requestAnimFrame = (() => {
        return typeof window !== "undefined" && window.requestAnimationFrame || typeof global !== "undefined" && global.requestAnimationFrame || ((callback) => setTimeout(callback, 16));
      })();
      const cancelAnimFrame = (() => {
        return typeof window !== "undefined" && window.cancelAnimationFrame || typeof global !== "undefined" && global.cancelAnimationFrame || clearTimeout;
      })();
      const props = __props;
      const cubeSize = vue.computed(() => props.size);
      const rotationX = vue.ref(0);
      const rotationY = vue.ref(0);
      const rotationZ = vue.ref(0);
      const isAnimating = vue.ref(false);
      let animationId = vue.ref(null);
      const faces = [
        { dots: 1, color: "#d32f2f" },
        // 1点红色
        { dots: 6, color: "#1565c0" },
        // 6点蓝色（1的对面）
        { dots: 3, color: "#1565c0" },
        // 3点蓝色
        { dots: 4, color: "#d32f2f" },
        // 4点红色（3的对面）
        { dots: 2, color: "#1565c0" },
        // 2点蓝色
        { dots: 5, color: "#1565c0" }
        // 5点蓝色（2的对面）
      ];
      const halfCubeSize = vue.computed(() => cubeSize.value / 2);
      const dotPadding = vue.computed(() => Math.max(cubeSize.value * 0.25, 15));
      const dotSize = vue.computed(() => Math.max(cubeSize.value * 0.12, 8));
      const cubeSceneStyle = vue.computed(() => ({
        width: `${cubeSize.value}rpx`,
        height: `${cubeSize.value}rpx`,
        transform: cubeTransform.value
      }));
      const faceStyle = vue.computed(() => ({
        width: `${cubeSize.value}rpx`,
        height: `${cubeSize.value}rpx`,
        padding: `${dotPadding.value}rpx`
      }));
      const setRotationForValue = (value) => {
        switch (value) {
          case 1:
            rotationX.value = 0;
            rotationY.value = 0;
            rotationZ.value = 0;
            break;
          case 2:
            rotationX.value = -90;
            rotationY.value = 0;
            rotationZ.value = 0;
            break;
          case 3:
            rotationX.value = 0;
            rotationY.value = -90;
            rotationZ.value = 0;
            break;
          case 4:
            rotationX.value = 0;
            rotationY.value = 90;
            rotationZ.value = 0;
            break;
          case 5:
            rotationX.value = 90;
            rotationY.value = 0;
            rotationZ.value = 0;
            break;
          case 6:
            rotationX.value = 0;
            rotationY.value = 180;
            rotationZ.value = 0;
            break;
          default:
            rotationX.value = 0;
            rotationY.value = 0;
            rotationZ.value = 0;
            break;
        }
      };
      const stopAnimation = () => {
        if (animationId.value) {
          cancelAnimFrame(animationId.value);
          animationId.value = null;
        }
        isAnimating.value = false;
        setRotationForValue(props.value);
      };
      const cubeTransform = vue.computed(
        () => `rotateX(${rotationX.value}deg) rotateY(${rotationY.value}deg) rotateZ(${rotationZ.value}deg)`
      );
      const updateDice = () => {
        smoothTransitionToValue(props.value);
      };
      const normalizeAngle = (angle) => {
        return (angle % 360 + 360) % 360;
      };
      const startContinuousAnimation = () => {
        stopAnimation();
        isAnimating.value = true;
        const baseSpeedX = 2.5 + Math.random() * 1;
        const baseSpeedY = 3.5 + Math.random() * 1;
        const baseSpeedZ = 1.5 + Math.random() * 1;
        const animate = () => {
          if (isAnimating.value) {
            rotationX.value += baseSpeedX + Math.sin(Date.now() * 1e-3) * 0.5;
            rotationY.value += baseSpeedY + Math.cos(Date.now() * 15e-4) * 0.5;
            rotationZ.value += baseSpeedZ + Math.sin(Date.now() * 8e-4) * 0.3;
            if (Math.abs(rotationX.value) > 3600) {
              const fullRotations = Math.floor(Math.abs(rotationX.value) / 3600) * 3600;
              rotationX.value -= Math.sign(rotationX.value) * fullRotations;
            }
            if (Math.abs(rotationY.value) > 3600) {
              const fullRotations = Math.floor(Math.abs(rotationY.value) / 3600) * 3600;
              rotationY.value -= Math.sign(rotationY.value) * fullRotations;
            }
            if (Math.abs(rotationZ.value) > 3600) {
              const fullRotations = Math.floor(Math.abs(rotationZ.value) / 3600) * 3600;
              rotationZ.value -= Math.sign(rotationZ.value) * fullRotations;
            }
            animationId.value = requestAnimFrame(animate);
          }
        };
        animate();
      };
      const smoothTransitionToValue = (targetValue) => {
        if (!props.animated) {
          setRotationForValue(targetValue);
          return;
        }
        stopAnimation();
        const startRotationX = rotationX.value;
        const startRotationY = rotationY.value;
        const startRotationZ = rotationZ.value;
        let baseTargetX = 0, baseTargetY = 0, baseTargetZ = 0;
        switch (targetValue) {
          case 1:
            baseTargetX = 0;
            baseTargetY = 0;
            baseTargetZ = 0;
            break;
          case 2:
            baseTargetX = -90;
            baseTargetY = 0;
            baseTargetZ = 0;
            break;
          case 3:
            baseTargetX = 0;
            baseTargetY = -90;
            baseTargetZ = 0;
            break;
          case 4:
            baseTargetX = 0;
            baseTargetY = 90;
            baseTargetZ = 0;
            break;
          case 5:
            baseTargetX = 90;
            baseTargetY = 0;
            baseTargetZ = 0;
            break;
          case 6:
            baseTargetX = 0;
            baseTargetY = 180;
            baseTargetZ = 0;
            break;
        }
        const extraRotations = 12 + Math.floor(Math.random() * 6);
        const targetX = startRotationX + extraRotations * 360 + baseTargetX;
        const targetY = startRotationY + extraRotations * 360 + baseTargetY;
        const targetZ = startRotationZ + extraRotations * 360 + baseTargetZ;
        const totalDuration = 1200;
        const startTime = Date.now();
        const animate = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / totalDuration, 1);
          const easeProgress = progress < 0.7 ? Math.pow(progress / 0.7, 0.15) * 0.9 : 0.9 + Math.pow((progress - 0.7) / 0.3, 2) * 0.1;
          rotationX.value = startRotationX + (targetX - startRotationX) * easeProgress;
          rotationY.value = startRotationY + (targetY - startRotationY) * easeProgress;
          rotationZ.value = startRotationZ + (targetZ - startRotationZ) * easeProgress;
          if (progress < 1) {
            animationId.value = requestAnimFrame(animate);
          } else {
            setRotationForValue(targetValue);
            isAnimating.value = false;
            if (animationId.value) {
              cancelAnimFrame(animationId.value);
              animationId.value = null;
            }
          }
        };
        isAnimating.value = true;
        animationId.value = requestAnimFrame(animate);
      };
      const restartContinuousAnimation = () => {
        stopAnimation();
        setTimeout(() => {
          rotationX.value = Math.random() * 360;
          rotationY.value = Math.random() * 360;
          rotationZ.value = Math.random() * 360;
          startContinuousAnimation();
        }, 100);
      };
      vue.watch(
        () => props.value,
        (newValue, oldValue) => {
          if (newValue !== oldValue && props.animated) {
            smoothTransitionToValue(newValue);
          } else if (newValue !== oldValue) {
            setRotationForValue(newValue);
          }
        }
      );
      __expose({
        restartContinuousAnimation,
        stopAnimation
        // 暴露停止动画方法
      });
      vue.onMounted(() => {
        setRotationForValue(props.value);
        setTimeout(() => {
          if (!isAnimating.value) {
            startContinuousAnimation();
          }
        }, 500);
      });
      vue.onBeforeUnmount(() => {
        stopAnimation();
      });
      const __returned__ = { requestAnimFrame, cancelAnimFrame, props, cubeSize, rotationX, rotationY, rotationZ, isAnimating, get animationId() {
        return animationId;
      }, set animationId(v) {
        animationId = v;
      }, faces, halfCubeSize, dotPadding, dotSize, cubeSceneStyle, faceStyle, setRotationForValue, stopAnimation, cubeTransform, updateDice, normalizeAngle, startContinuousAnimation, smoothTransitionToValue, restartContinuousAnimation, ref: vue.ref, computed: vue.computed, onMounted: vue.onMounted, onBeforeUnmount: vue.onBeforeUnmount, watch: vue.watch };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      "view",
      {
        class: "cube-container",
        style: vue.normalizeStyle({
          perspective: `${$setup.cubeSize * 6}rpx`,
          "--cube-size": `${$setup.cubeSize}rpx`
        })
      },
      [
        vue.createCommentVNode(" Canvas容器，使用Transform来控制立方体模型 "),
        vue.createElementVNode(
          "view",
          {
            class: "cube-scene",
            style: vue.normalizeStyle({
              ...$setup.cubeSceneStyle,
              "--half-cube-size": `${$setup.halfCubeSize}rpx`,
              "--dot-size": `${$setup.dotSize}rpx`
            })
          },
          [
            vue.createCommentVNode(" 立方体的6个面，直接使用flex布局 "),
            (vue.openBlock(), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($setup.faces, (face, index) => {
                return vue.createElementVNode(
                  "view",
                  {
                    key: index,
                    class: vue.normalizeClass([`cube-face face-${index + 1} dice-face-${face.dots}`, "face-element"]),
                    style: vue.normalizeStyle($setup.faceStyle)
                  },
                  [
                    vue.createCommentVNode(" 使用flex布局直接放置点数 "),
                    (vue.openBlock(true), vue.createElementBlock(
                      vue.Fragment,
                      null,
                      vue.renderList(face.dots, (dotIndex) => {
                        return vue.openBlock(), vue.createElementBlock(
                          "view",
                          {
                            key: dotIndex,
                            class: vue.normalizeClass(["dot", face.color === "#d32f2f" ? "red-dot" : "blue-dot"]),
                            style: vue.normalizeStyle({
                              width: `${$setup.dotSize}rpx`,
                              height: `${$setup.dotSize}rpx`
                            })
                          },
                          null,
                          6
                          /* CLASS, STYLE */
                        );
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    ))
                  ],
                  6
                  /* CLASS, STYLE */
                );
              }),
              64
              /* STABLE_FRAGMENT */
            ))
          ],
          4
          /* STYLE */
        )
      ],
      4
      /* STYLE */
    );
  }
  const Dice = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__scopeId", "data-v-f0b1c87e"], ["__file", "/Users/<USER>/02-workspace/08_HbuilderSpace/top.resty.dice_game/components/dice.vue"]]);
  const _sfc_main$1 = {
    __name: "index",
    setup(__props, { expose: __expose }) {
      __expose();
      const diceValues = vue.ref([1, 2, 3]);
      const gameResult = vue.ref(null);
      const isRolling = vue.ref(false);
      const selectedBeer = vue.ref(BeverageType.YUAN_JIANG);
      const showRulesModal = vue.ref(false);
      const showAnalysisModal = vue.ref(false);
      const allHistory = vue.ref([]);
      const diceRefs = vue.ref([]);
      const buttonsDisabled = vue.ref(false);
      let restartAnimationTimer = null;
      const showJackpotAnimation = vue.ref(false);
      const jackpotDice = vue.ref([]);
      let jackpotAnimationTimer = null;
      const showRulesPanel = vue.ref(true);
      const showHistoryPanel = vue.ref(true);
      const rulesAnimationClass = vue.ref("");
      const historyAnimationClass = vue.ref("");
      const deviceType = vue.ref("");
      const recentHistory = vue.computed(() => {
        return [...allHistory.value].reverse();
      });
      const middleAreaClass = vue.computed(() => {
        const classes = [];
        if (!showRulesPanel.value)
          classes.push("hide-rules");
        if (!showHistoryPanel.value)
          classes.push("hide-history");
        if (!showRulesPanel.value && !showHistoryPanel.value)
          classes.push("hide-both");
        return classes.join(" ");
      });
      onLoad(() => {
        let info = uni.getSystemInfoSync();
        formatAppLog("log", "at pages/index/index.vue:283", "app is onload... ", info.deviceType);
        deviceType.value = info.deviceType;
        if (info.deviceType === "phone") {
          showRulesPanel.value = false;
          showHistoryPanel.value = false;
        }
        loadHistory();
      });
      const onShow = () => {
        if (!gameResult.value && !isRolling.value) {
          setTimeout(() => {
            restartDiceAnimations();
          }, 500);
        }
      };
      uni.onShow && uni.onShow(onShow);
      const loadHistory = () => {
        try {
          let savedHistory = uni.getStorageSync("dice_records");
          if (savedHistory) {
            allHistory.value = JSON.parse(savedHistory);
            return;
          }
          savedHistory = uni.getStorageSync("diceGameHistory");
          if (savedHistory) {
            allHistory.value = JSON.parse(savedHistory);
            uni.setStorageSync("dice_records", savedHistory);
            uni.removeStorageSync("diceGameHistory");
          } else {
            allHistory.value = [];
          }
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:330", "加载历史记录失败:", e);
          allHistory.value = [];
        }
      };
      const saveGameResult = (result) => {
        try {
          const historyItem = {
            dice: result.dice,
            diceType: result.diceType,
            weight: result.weight,
            beerType: result.beerType,
            timestamp: Date.now(),
            date: (/* @__PURE__ */ new Date()).toLocaleString()
          };
          allHistory.value.push(historyItem);
          uni.setStorageSync("dice_records", JSON.stringify(allHistory.value));
          diceCalculator.saveHistory(historyItem);
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:357", "保存游戏结果失败:", e);
        }
      };
      const clearHistory = () => {
        try {
          allHistory.value = [];
          diceCalculator.clearHistory();
          if (showJackpotAnimation.value) {
            showJackpotAnimation.value = false;
            jackpotDice.value = [];
          }
          if (jackpotAnimationTimer) {
            clearTimeout(jackpotAnimationTimer);
            jackpotAnimationTimer = null;
          }
          if (restartAnimationTimer) {
            clearTimeout(restartAnimationTimer);
            restartAnimationTimer = null;
          }
          gameResult.value = null;
          isRolling.value = false;
          buttonsDisabled.value = false;
          uni.showToast({
            title: "记录已清除",
            icon: "success"
          });
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:393", "清除记录失败:", e);
          uni.showToast({
            title: "清除失败",
            icon: "error"
          });
        }
      };
      const restartDiceAnimations = () => {
        diceValues.value = [1, 2, 3];
        vue.nextTick(() => {
          diceRefs.value.forEach((diceRef) => {
            if (diceRef && diceRef.restartContinuousAnimation) {
              diceRef.restartContinuousAnimation();
            }
          });
        });
      };
      const selectBeer = (type) => {
        if (buttonsDisabled.value || isRolling.value) {
          return;
        }
        selectedBeer.value = type;
        rollDice();
      };
      const rollDice = () => {
        if (isRolling.value) {
          return;
        }
        if (restartAnimationTimer) {
          clearTimeout(restartAnimationTimer);
          restartAnimationTimer = null;
        }
        const result = diceCalculator.calculateGameResult(selectedBeer.value);
        gameResult.value = null;
        isRolling.value = true;
        buttonsDisabled.value = true;
        let animationCount = 0;
        const quickAnimations = 12;
        let currentSpeed = 50;
        const quickRoll = () => {
          const randomDice = diceCalculator.generateDice();
          diceValues.value = randomDice;
          animationCount++;
          if (animationCount >= quickAnimations) {
            startFinalAnimation();
          } else {
            currentSpeed = Math.max(currentSpeed - 3, 25);
            setTimeout(quickRoll, currentSpeed);
          }
        };
        const startFinalAnimation = () => {
          diceValues.value = [...result.dice];
          setTimeout(() => {
            diceRefs.value.forEach((diceRef, index) => {
              if (diceRef && diceRef.stopAnimation) {
                diceRef.stopAnimation();
              }
            });
            diceValues.value = [...result.dice];
            setTimeout(() => {
              isRolling.value = false;
              gameResult.value = result;
              saveGameResult(result);
              loadHistory();
              if (result.diceType === "豹子") {
                showJackpotAnimation.value = true;
                jackpotDice.value = [...result.dice];
                jackpotAnimationTimer = setTimeout(() => {
                  showJackpotAnimation.value = false;
                  jackpotDice.value = [];
                }, 3e3);
              }
              restartAnimationTimer = setTimeout(() => {
                gameResult.value = null;
                buttonsDisabled.value = false;
                restartDiceAnimations();
              }, 2e3);
            }, 200);
          }, 1500);
        };
        quickRoll();
      };
      const toggleRulesPanel = () => {
        if (showRulesPanel.value) {
          rulesAnimationClass.value = "animate__slideOutLeft";
          setTimeout(() => {
            showRulesPanel.value = false;
            rulesAnimationClass.value = "";
          }, 500);
        } else {
          showRulesPanel.value = true;
          rulesAnimationClass.value = "animate__slideInLeft";
          setTimeout(() => {
            rulesAnimationClass.value = "";
          }, 500);
        }
      };
      const toggleHistoryPanel = () => {
        if (showHistoryPanel.value) {
          historyAnimationClass.value = "animate__slideOutRight";
          setTimeout(() => {
            showHistoryPanel.value = false;
            historyAnimationClass.value = "";
          }, 500);
        } else {
          showHistoryPanel.value = true;
          historyAnimationClass.value = "animate__slideInRight";
          setTimeout(() => {
            historyAnimationClass.value = "";
          }, 500);
        }
      };
      const showRules = () => {
        showRulesModal.value = true;
      };
      const closeRulesModal = () => {
        showRulesModal.value = false;
      };
      const showHistory = () => {
        uni.navigateTo({
          url: "/pages/history/history"
        });
      };
      const handleRulesClick = () => {
        if (deviceType.value === "phone") {
          showRules();
        } else {
          toggleRulesPanel();
        }
      };
      const handleHistoryClick = () => {
        if (deviceType.value === "phone") {
          showHistory();
        } else {
          toggleHistoryPanel();
        }
      };
      const getHistoryItemClass = (diceType) => {
        switch (diceType) {
          case "散花":
            return "history-sanhua";
          case "对子":
            return "history-duizi";
          case "顺子":
            return "history-shunzi";
          case "豹子":
            return "history-baozi";
          default:
            return "";
        }
      };
      const getResultTypeClass = (diceType) => {
        switch (diceType) {
          case "散花":
            return "result-sanhua";
          case "对子":
            return "result-duizi";
          case "顺子":
            return "result-shunzi";
          case "豹子":
            return "result-baozi";
          default:
            return "";
        }
      };
      const getDiceSize = () => {
        if (deviceType.value === "phone") {
          return 50;
        } else {
          try {
            const systemInfo = uni.getSystemInfoSync();
            const screenWidth = systemInfo.screenWidth || systemInfo.windowWidth || 768;
            if (screenWidth >= 1024) {
              return 60;
            } else {
              return 50;
            }
          } catch (e) {
            return deviceType.value === "pc" ? 100 : 80;
          }
        }
      };
      vue.onMounted(() => {
        diceValues.value = [1, 2, 3];
        gameResult.value = null;
        vue.nextTick(() => {
          restartDiceAnimations();
        });
      });
      const __returned__ = { diceValues, gameResult, isRolling, selectedBeer, showRulesModal, showAnalysisModal, allHistory, diceRefs, buttonsDisabled, get restartAnimationTimer() {
        return restartAnimationTimer;
      }, set restartAnimationTimer(v) {
        restartAnimationTimer = v;
      }, showJackpotAnimation, jackpotDice, get jackpotAnimationTimer() {
        return jackpotAnimationTimer;
      }, set jackpotAnimationTimer(v) {
        jackpotAnimationTimer = v;
      }, showRulesPanel, showHistoryPanel, rulesAnimationClass, historyAnimationClass, deviceType, recentHistory, middleAreaClass, onShow, loadHistory, saveGameResult, clearHistory, restartDiceAnimations, selectBeer, rollDice, toggleRulesPanel, toggleHistoryPanel, showRules, closeRulesModal, showHistory, handleRulesClick, handleHistoryClick, getHistoryItemClass, getResultTypeClass, getDiceSize, ref: vue.ref, onMounted: vue.onMounted, computed: vue.computed, nextTick: vue.nextTick, get onLoad() {
        return onLoad;
      }, get diceCalculator() {
        return diceCalculator;
      }, get BeverageType() {
        return BeverageType;
      }, RulesModal, AnalysisModal, Dice };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "game-container" }, [
      vue.createCommentVNode(" 上部：标题栏 "),
      vue.createElementVNode("view", { class: "header-area" }, [
        vue.createElementVNode("button", {
          class: "rules-btn",
          onClick: $setup.handleRulesClick
        }, "📋 游戏规则"),
        vue.createElementVNode("view", { class: "title-section" }, [
          vue.createElementVNode("text", { class: "main-title" }, "🎲 骰子啤酒游戏"),
          vue.createElementVNode("text", { class: "sub-title" }, "摇出点数，畅饮啤酒")
        ]),
        vue.createElementVNode("view", { class: "header-buttons" }, [
          vue.createElementVNode("button", {
            class: "header-btn",
            onClick: $setup.handleHistoryClick
          }, "游戏记录")
        ])
      ]),
      vue.createCommentVNode(" 中部：三栏布局 "),
      vue.createElementVNode(
        "view",
        {
          class: vue.normalizeClass(["middle-area", $setup.middleAreaClass])
        },
        [
          vue.createCommentVNode(" 左：游戏规则面板 "),
          vue.withDirectives(vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["rules-panel animate__animated", $setup.rulesAnimationClass])
            },
            [
              vue.createElementVNode("text", { class: "panel-title" }, "🎲 游戏规则"),
              vue.createElementVNode("view", { class: "rules-content" }, [
                vue.createElementVNode("view", { class: "rule-section" }, [
                  vue.createElementVNode("view", { class: "rule-list" }, [
                    vue.createElementVNode("view", { class: "rule-item-wrapper" }, [
                      vue.createElementVNode("text", { class: "rule-item rule-sanhua" }, "散花"),
                      vue.createElementVNode("text", { class: "rule-desc rule-sanhua" }, "三个不同点数"),
                      vue.createElementVNode("text", { class: "rule-reward rule-sanhua" }, "1斤啤酒")
                    ]),
                    vue.createElementVNode("view", { class: "rule-item-wrapper" }, [
                      vue.createElementVNode("text", { class: "rule-item rule-duizi" }, "对子"),
                      vue.createElementVNode("text", { class: "rule-desc rule-duizi" }, "两个相同点数"),
                      vue.createElementVNode("text", { class: "rule-reward rule-duizi" }, "2斤啤酒")
                    ]),
                    vue.createElementVNode("view", { class: "rule-item-wrapper" }, [
                      vue.createElementVNode("text", { class: "rule-item rule-shunzi" }, "顺子"),
                      vue.createElementVNode("text", { class: "rule-desc rule-shunzi" }, "连续三个点数"),
                      vue.createElementVNode("text", { class: "rule-reward rule-shunzi" }, "3斤啤酒")
                    ]),
                    vue.createElementVNode("view", { class: "rule-item-wrapper" }, [
                      vue.createElementVNode("text", { class: "rule-item rule-baozi" }, "豹子"),
                      vue.createElementVNode("text", { class: "rule-desc rule-baozi" }, "三个相同点数"),
                      vue.createElementVNode("text", { class: "rule-reward rule-baozi" }, "4斤啤酒")
                    ])
                  ])
                ]),
                vue.createElementVNode("view", { class: "rule-section" }, [
                  vue.createElementVNode("text", { class: "section-subtitle" }, "💰 特殊优惠"),
                  vue.createElementVNode("text", { class: "rule-extra rule-baozi" }, "加10元可换3升原浆"),
                  vue.createElementVNode("text", { class: "rule-extra rule-baozi" }, "架子可退或换1斤原浆")
                ])
              ])
            ],
            2
            /* CLASS */
          ), [
            [vue.vShow, $setup.showRulesPanel]
          ]),
          vue.createCommentVNode(" 中：游戏区域 "),
          vue.createElementVNode("view", { class: "game-area" }, [
            vue.createElementVNode("view", { class: "dice-container" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.diceValues, (dice, index) => {
                  return vue.openBlock(), vue.createBlock($setup["Dice"], {
                    key: index,
                    ref_for: true,
                    ref: (el) => $setup.diceRefs[index] = el,
                    value: dice,
                    size: $setup.getDiceSize(),
                    animated: true
                  }, null, 8, ["value", "size"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ]),
            vue.createElementVNode("view", { class: "game-result" }, [
              vue.createElementVNode("view", { class: "result-content" }, [
                $setup.isRolling ? (vue.openBlock(), vue.createElementBlock("text", {
                  key: 0,
                  class: "result-rolling"
                }, "🎲 摇骰子中...")) : !$setup.gameResult ? (vue.openBlock(), vue.createElementBlock("text", {
                  key: 1,
                  class: "result-prompt"
                }, "🎰 摇出大奖，畅饮啤酒！")) : (vue.openBlock(), vue.createElementBlock(
                  vue.Fragment,
                  { key: 2 },
                  [
                    vue.createElementVNode("view", { class: "dice-display" }, [
                      vue.createElementVNode(
                        "text",
                        { class: "dice-numbers" },
                        vue.toDisplayString($setup.gameResult.dice.join(" - ")),
                        1
                        /* TEXT */
                      )
                    ]),
                    vue.createElementVNode(
                      "text",
                      {
                        class: vue.normalizeClass(["result-type", $setup.getResultTypeClass($setup.gameResult.diceType)])
                      },
                      vue.toDisplayString($setup.gameResult.diceType) + " " + vue.toDisplayString($setup.gameResult.weight) + " 斤 " + vue.toDisplayString($setup.gameResult.beerType === "draft" ? "原浆" : "果啤"),
                      3
                      /* TEXT, CLASS */
                    )
                  ],
                  64
                  /* STABLE_FRAGMENT */
                ))
              ])
            ])
          ]),
          vue.createCommentVNode(" 右：游戏记录面板 "),
          vue.withDirectives(vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["history-panel animate__animated", $setup.historyAnimationClass])
            },
            [
              vue.createElementVNode("view", { class: "panel-header" }, [
                vue.createElementVNode("text", { class: "panel-title" }, "📊 游戏记录")
              ]),
              vue.createElementVNode("view", { class: "history-content" }, [
                $setup.allHistory.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "no-history"
                }, [
                  vue.createElementVNode("text", null, "暂无记录")
                ])) : (vue.openBlock(), vue.createElementBlock("view", {
                  key: 1,
                  class: "history-list"
                }, [
                  (vue.openBlock(true), vue.createElementBlock(
                    vue.Fragment,
                    null,
                    vue.renderList($setup.recentHistory, (record, index) => {
                      return vue.openBlock(), vue.createElementBlock(
                        "view",
                        {
                          key: index,
                          class: vue.normalizeClass(["history-item", $setup.getHistoryItemClass(record.点数类型 || record.diceType)])
                        },
                        [
                          vue.createElementVNode("view", { class: "history-dice-container" }, [
                            (vue.openBlock(true), vue.createElementBlock(
                              vue.Fragment,
                              null,
                              vue.renderList(record.骰子点数 || record.dice, (dice, diceIndex) => {
                                return vue.openBlock(), vue.createElementBlock("view", {
                                  key: diceIndex,
                                  class: "history-dice-single"
                                }, [
                                  vue.createElementVNode(
                                    "text",
                                    { class: "history-dice-number" },
                                    vue.toDisplayString(dice),
                                    1
                                    /* TEXT */
                                  )
                                ]);
                              }),
                              128
                              /* KEYED_FRAGMENT */
                            ))
                          ]),
                          vue.createElementVNode(
                            "text",
                            { class: "history-type" },
                            vue.toDisplayString(record.点数类型 || record.diceType),
                            1
                            /* TEXT */
                          )
                        ],
                        2
                        /* CLASS */
                      );
                    }),
                    128
                    /* KEYED_FRAGMENT */
                  ))
                ]))
              ])
            ],
            2
            /* CLASS */
          ), [
            [vue.vShow, $setup.showHistoryPanel]
          ])
        ],
        2
        /* CLASS */
      ),
      vue.createCommentVNode(" 中大奖动画 "),
      $setup.showJackpotAnimation ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "jackpot-overlay"
      }, [
        vue.createElementVNode("view", { class: "jackpot-container" }, [
          vue.createElementVNode("view", { class: "jackpot-fireworks" }, [
            vue.createElementVNode("view", { class: "firework firework-1" }),
            vue.createElementVNode("view", { class: "firework firework-2" }),
            vue.createElementVNode("view", { class: "firework firework-3" }),
            vue.createElementVNode("view", { class: "firework firework-4" })
          ]),
          vue.createElementVNode("view", { class: "jackpot-content" }, [
            vue.createElementVNode("text", { class: "jackpot-title" }, "🎉 中大奖啦！🎉"),
            vue.createElementVNode("text", { class: "jackpot-subtitle" }, "恭喜获得豹子！"),
            vue.createElementVNode("view", { class: "jackpot-dice" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.jackpotDice, (dice, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    key: index,
                    class: "jackpot-dice-item"
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "jackpot-dice-number" },
                      vue.toDisplayString(dice),
                      1
                      /* TEXT */
                    )
                  ]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ]),
            vue.createElementVNode("text", { class: "jackpot-reward" }, "奖励：4斤啤酒")
          ]),
          vue.createElementVNode("view", { class: "jackpot-sparkles" }, [
            vue.createElementVNode("view", { class: "sparkle sparkle-1" }, "✨"),
            vue.createElementVNode("view", { class: "sparkle sparkle-2" }, "⭐"),
            vue.createElementVNode("view", { class: "sparkle sparkle-3" }, "💫"),
            vue.createElementVNode("view", { class: "sparkle sparkle-4" }, "🌟"),
            vue.createElementVNode("view", { class: "sparkle sparkle-5" }, "✨"),
            vue.createElementVNode("view", { class: "sparkle sparkle-6" }, "⭐")
          ])
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 下部：啤酒选择按钮 "),
      vue.createElementVNode("view", { class: "bottom-area" }, [
        vue.createCommentVNode(" 右上角按钮组 "),
        vue.createElementVNode("view", { class: "bottom-actions" }, [
          vue.createElementVNode("button", {
            class: "action-btn clear-btn",
            onClick: $setup.clearHistory
          }, " 清除记录 "),
          vue.createElementVNode("button", {
            class: "action-btn analysis-btn",
            onClick: _cache[0] || (_cache[0] = ($event) => $setup.showAnalysisModal = true)
          }, " 记录分析 ")
        ]),
        vue.createCommentVNode(" 啤酒选择按钮 "),
        vue.createElementVNode("view", { class: "beer-buttons" }, [
          vue.createElementVNode("button", {
            class: vue.normalizeClass(["beer-btn draft-btn", {
              active: $setup.selectedBeer === $setup.BeverageType.YUAN_JIANG,
              disabled: $setup.buttonsDisabled || $setup.isRolling
            }]),
            disabled: $setup.buttonsDisabled || $setup.isRolling,
            onClick: _cache[1] || (_cache[1] = ($event) => $setup.selectBeer($setup.BeverageType.YUAN_JIANG))
          }, [
            vue.createElementVNode("view", { class: "btn-content" }, [
              vue.createElementVNode("text", { class: "btn-icon" }, "🍺"),
              vue.createElementVNode("view", { class: "btn-text" }, [
                vue.createElementVNode("text", { class: "btn-title" }, "原浆啤酒"),
                vue.createElementVNode("text", { class: "btn-price" }, "¥ 10 元/次")
              ])
            ])
          ], 10, ["disabled"]),
          vue.createElementVNode("button", {
            class: vue.normalizeClass(["beer-btn fruit-btn", {
              active: $setup.selectedBeer === $setup.BeverageType.GUO_PI,
              disabled: $setup.buttonsDisabled || $setup.isRolling
            }]),
            disabled: $setup.buttonsDisabled || $setup.isRolling,
            onClick: _cache[2] || (_cache[2] = ($event) => $setup.selectBeer($setup.BeverageType.GUO_PI))
          }, [
            vue.createElementVNode("view", { class: "btn-content" }, [
              vue.createElementVNode("text", { class: "btn-icon" }, "🍹"),
              vue.createElementVNode("view", { class: "btn-text" }, [
                vue.createElementVNode("text", { class: "btn-title" }, "果味啤酒"),
                vue.createElementVNode("text", { class: "btn-price" }, "¥ 15 元/次")
              ])
            ])
          ], 10, ["disabled"])
        ])
      ]),
      vue.createCommentVNode(" 规则弹窗 "),
      vue.createVNode($setup["RulesModal"], {
        visible: $setup.showRulesModal,
        onClose: $setup.closeRulesModal
      }, null, 8, ["visible"]),
      vue.createCommentVNode(" 记录分析弹窗 "),
      vue.createVNode($setup["AnalysisModal"], {
        visible: $setup.showAnalysisModal,
        history: $setup.allHistory,
        onClose: _cache[3] || (_cache[3] = ($event) => $setup.showAnalysisModal = false)
      }, null, 8, ["visible", "history"])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"], ["__file", "/Users/<USER>/02-workspace/08_HbuilderSpace/top.resty.dice_game/pages/index/index.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "/Users/<USER>/02-workspace/08_HbuilderSpace/top.resty.dice_game/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    app.config.errorHandler = (err, vm, info) => {
      formatAppLog("error", "at main.js:34", "Vue Error:", err, info);
    };
    app.config.warnHandler = (msg, vm, trace) => {
      formatAppLog("warn", "at main.js:39", "Vue Warning:", msg, trace);
    };
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
