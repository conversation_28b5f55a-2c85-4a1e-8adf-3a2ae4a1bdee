"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var D=Object.create;var b=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var M=Object.getPrototypeOf,I=Object.prototype.hasOwnProperty;var V=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var L=(e,t,a,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of x(t))!I.call(e,r)&&r!==a&&b(e,r,{get:()=>t[r],enumerable:!(i=C(t,r))||i.enumerable});return e};var N=(e,t,a)=>(a=e!=null?D(M(e)):{},L(t||!e||!e.__esModule?b(a,"default",{value:e,enumerable:!0}):a,e));var A=V((U,v)=>{v.exports=Vue});var _={data(){return{locale:"en",fallbackLocale:"en",localization:{en:{done:"OK",cancel:"Cancel"},zh:{done:"\u5B8C\u6210",cancel:"\u53D6\u6D88"},"zh-hans":{},"zh-hant":{},messages:{}},localizationTemplate:{}}},onLoad(){this.initLocale()},created(){this.initLocale()},methods:{initLocale(){if(this.__initLocale)return;this.__initLocale=!0;let e=(plus.webview.currentWebview().extras||{}).data||{};if(e.messages&&(this.localization.messages=e.messages),e.locale){this.locale=e.locale.toLowerCase();return}let t={chs:"hans",cn:"hans",sg:"hans",cht:"hant",tw:"hant",hk:"hant",mo:"hant"},a=plus.os.language.toLowerCase().split("/")[0].replace("_","-").split("-"),i=a[1];i&&(a[1]=t[i]||i),a.length=a.length>2?2:a.length,this.locale=a.join("-")},localize(e){let t=this.locale,a=t.split("-")[0],i=this.fallbackLocale,r=n=>Object.assign({},this.localization[n],(this.localizationTemplate||{})[n]);return r("messages")[e]||r(t)[e]||r(a)[e]||r(i)[e]||e}}},k={onLoad(){this.initMessage()},methods:{initMessage(){let{from:e,callback:t,runtime:a,data:i={},useGlobalEvent:r}=plus.webview.currentWebview().extras||{};this.__from=e,this.__runtime=a,this.__page=plus.webview.currentWebview().id,this.__useGlobalEvent=r,this.data=JSON.parse(JSON.stringify(i)),plus.key.addEventListener("backbutton",()=>{typeof this.onClose=="function"?this.onClose():plus.webview.currentWebview().close("auto")});let n=this,c=function(o){let u=o.data&&o.data.__message;!u||n.__onMessageCallback&&n.__onMessageCallback(u.data)};if(this.__useGlobalEvent)weex.requireModule("globalEvent").addEventListener("plusMessage",c);else{let o=new BroadcastChannel(this.__page);o.onmessage=c}},postMessage(e={},t=!1){let a=JSON.parse(JSON.stringify({__message:{__page:this.__page,data:e,keep:t}})),i=this.__from;if(this.__runtime==="v8")this.__useGlobalEvent?plus.webview.postMessageToUniNView(a,i):new BroadcastChannel(i).postMessage(a);else{let r=plus.webview.getWebviewById(i);r&&r.evalJS(`__plusMessage&&__plusMessage(${JSON.stringify({data:a})})`)}},onMessage(e){this.__onMessageCallback=e}}};var s=N(A());var m=(e,t)=>{let a=e.__vccOpts||e;for(let[i,r]of t)a[i]=r;return a};var d=e=>e>9?e:"0"+e;function w({date:e=new Date,mode:t="date"}){return t==="time"?d(e.getHours())+":"+d(e.getMinutes()):e.getFullYear()+"-"+d(e.getMonth()+1)+"-"+d(e.getDate())}var O={data(){return{darkmode:!1,theme:"light"}},onLoad(){this.initDarkmode()},created(){this.initDarkmode()},computed:{isDark(){return this.theme==="dark"}},methods:{initDarkmode(){if(this.__init)return;this.__init=!0;let e=(plus.webview.currentWebview().extras||{}).data||{};this.darkmode=e.darkmode||!1,this.darkmode&&(this.theme=e.theme||"light")}}},z={data(){return{safeAreaInsets:{left:0,right:0,top:0,bottom:0}}},onLoad(){this.initSafeAreaInsets()},created(){this.initSafeAreaInsets()},methods:{initSafeAreaInsets(){if(this.__initSafeAreaInsets)return;this.__initSafeAreaInsets=!0;let e=plus.webview.currentWebview();e.addEventListener("resize",()=>{setTimeout(()=>{this.updateSafeAreaInsets(e)},20)}),this.updateSafeAreaInsets(e)},updateSafeAreaInsets(e){let t=e.getSafeAreaInsets(),a=this.safeAreaInsets;Object.keys(a).forEach(i=>{a[i]=t[i]})}}},Y={content:{"":{position:"absolute",top:0,left:0,bottom:0,right:0}},"uni-mask":{"":{position:"absolute",top:0,left:0,bottom:0,right:0,backgroundColor:"rgba(0,0,0,0.4)",opacity:0,transitionProperty:"opacity",transitionDuration:200,transitionTimingFunction:"linear"}},"uni-mask-visible":{"":{opacity:1}},"uni-picker":{"":{position:"absolute",left:0,bottom:0,right:0,backgroundColor:"#ffffff",color:"#000000",flexDirection:"column",transform:"translateY(295px)"}},"uni-picker-header":{"":{height:45,borderBottomWidth:.5,borderBottomColor:"#C8C9C9",backgroundColor:"#FFFFFF",fontSize:20}},"uni-picker-action":{"":{position:"absolute",textAlign:"center",top:0,height:45,paddingTop:0,paddingRight:14,paddingBottom:0,paddingLeft:14,fontSize:17,lineHeight:45}},"uni-picker-action-cancel":{"":{left:0,color:"#888888"}},"uni-picker-action-confirm":{"":{right:0,color:"#007aff"}},"uni-picker-content":{"":{flex:1}},"uni-picker-dark":{"":{backgroundColor:"#232323"}},"uni-picker-header-dark":{"":{backgroundColor:"#232323",borderBottomColor:"rgba(255,255,255,0.05)"}},"uni-picker-action-cancel-dark":{"":{color:"rgba(255,255,255,0.8)"}},"@TRANSITION":{"uni-mask":{property:"opacity",duration:200,timingFunction:"linear"}}};function S(){if(this.mode===l.TIME)return"00:00";if(this.mode===l.DATE){let e=new Date().getFullYear()-61;switch(this.fields){case h.YEAR:return e;case h.MONTH:return e+"-01";default:return e+"-01-01"}}return""}function E(){if(this.mode===l.TIME)return"23:59";if(this.mode===l.DATE){let e=new Date().getFullYear()+61;switch(this.fields){case h.YEAR:return e;case h.MONTH:return e+"-12";default:return e+"-12-31"}}return""}function F(e){let t=new Date().getFullYear(),a=t-61,i=t+61;if(e.start){let r=new Date(e.start).getFullYear();!isNaN(r)&&r<a&&(a=r)}if(e.end){let r=new Date(e.start).getFullYear();!isNaN(r)&&r>i&&(i=r)}return{start:a,end:i}}var T=weex.requireModule("animation"),l={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date",REGION:"region"},h={YEAR:"year",MONTH:"month",DAY:"day"},g=!1,R={name:"Picker",mixins:[_,z,O],props:{pageId:{type:Number,default:0},range:{type:Array,default(){return[]}},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:l.SELECTOR},fields:{type:String,default:h.DAY},start:{type:String,default:S},end:{type:String,default:E},disabled:{type:[Boolean,String],default:!1},visible:{type:Boolean,default:!1}},data(){return{valueSync:null,timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],fontSize:16,height:261,android:weex.config.env.platform.toLowerCase()==="android"}},computed:{rangeArray(){var e=this.range;switch(this.mode){case l.SELECTOR:return[e];case l.MULTISELECTOR:return e;case l.TIME:return this.timeArray;case l.DATE:{let t=this.dateArray;switch(this.fields){case h.YEAR:return[t[0]];case h.MONTH:return[t[0],t[1]];default:return[t[0],t[1],t[2]]}}}return[]},startArray(){return this._getDateValueArray(this.start,S.bind(this)())},endArray(){return this._getDateValueArray(this.end,E.bind(this)())},textMaxLength(){return Math.floor(Math.min(weex.config.env.deviceWidth,weex.config.env.deviceHeight)/(this.fontSize*weex.config.env.scale+1)/this.rangeArray.length)},maskStyle(){return{opacity:this.visible?1:0,"background-color":this.android?"rgba(0, 0, 0, 0.6)":"rgba(0, 0, 0, 0.4)"}},pickerViewIndicatorStyle(){return`height: 34px;border-color:${this.isDark?"rgba(255, 255, 255, 0.05)":"#C8C9C9"};border-top-width:0.5px;border-bottom-width:0.5px;`},pickerViewColumnTextStyle(){return{fontSize:this.fontSize+"px","line-height":"34px","text-align":"center",color:this.isDark?"rgba(255, 255, 255, 0.8)":"#000"}},pickerViewMaskTopStyle(){return this.isDark?"background-image: linear-gradient(to bottom, rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));":""},pickerViewMaskBottomStyle(){return this.isDark?"background-image: linear-gradient(to top,rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));":""}},watch:{value(){this._setValueSync()},mode(){this._setValueSync()},range(){this._setValueSync()},valueSync(){this._setValueArray(),g=!0},valueArray(e){if(this.mode===l.TIME||this.mode===l.DATE){let t=this.mode===l.TIME?this._getTimeValue:this._getDateValue,a=this.valueArray,i=this.startArray,r=this.endArray;if(this.mode===l.DATE){let n=this.dateArray,c=n[2].length,o=Number(n[2][a[2]])||1,u=new Date(`${n[0][a[0]]}/${n[1][a[1]]}/${o}`).getDate();u<o&&(a[2]-=u+c-o)}t(a)<t(i)?this._cloneArray(a,i):t(a)>t(r)&&this._cloneArray(a,r)}e.forEach((t,a)=>{t!==this.oldValueArray[a]&&(this.oldValueArray[a]=t,this.mode===l.MULTISELECTOR&&this.$emit("columnchange",{column:a,value:t}))})},visible(e){e?setTimeout(()=>{T.transition(this.$refs.picker,{styles:{transform:"translateY(0)"},duration:200})},20):T.transition(this.$refs.picker,{styles:{transform:`translateY(${283+this.safeAreaInsets.bottom}px)`},duration:200})}},created(){this._createTime(),this._createDate(),this._setValueSync()},methods:{getTexts(e,t){let a=this.textMaxLength;return e.map(i=>{let r=String(typeof i=="object"?i[this.rangeKey]||"":this._l10nItem(i,t));if(a>0&&r.length>a){let n=0,c=0;for(let o=0;o<r.length;o++){let u=r.charCodeAt(o);if(u>127||u===94?n+=1:n+=.65,n<=a-1&&(c=o),n>=a)return o===r.length-1?r:r.substr(0,c+1)+"\u2026"}}return r||" "}).join(`
`)},_createTime(){var e=[],t=[];e.splice(0,e.length);for(let a=0;a<24;a++)e.push((a<10?"0":"")+a);t.splice(0,t.length);for(let a=0;a<60;a++)t.push((a<10?"0":"")+a);this.timeArray.push(e,t)},_createDate(){var e=[],t=F(this);for(let r=t.start,n=t.end;r<=n;r++)e.push(String(r));var a=[];for(let r=1;r<=12;r++)a.push((r<10?"0":"")+r);var i=[];for(let r=1;r<=31;r++)i.push((r<10?"0":"")+r);this.dateArray.push(e,a,i)},_getTimeValue(e){return e[0]*60+e[1]},_getDateValue(e){return e[0]*31*12+(e[1]||0)*31+(e[2]||0)},_cloneArray(e,t){for(let a=0;a<e.length&&a<t.length;a++)e[a]=t[a]},_setValueSync(){let e=this.value;switch(g=!1,this.mode){case l.MULTISELECTOR:Array.isArray(e)||(e=[]),Array.isArray(this.valueSync)||(this.valueSync=[]);let t=this.valueSync.length=Math.max(e.length,this.range.length);for(let a=0;a<t;a++){let i=Number(e[a]),r=Number(this.valueSync[a]),n=isNaN(i)?isNaN(r)?0:r:i,c=this.range[a]?this.range[a].length-1:0;this.valueSync.splice(a,1,n<0||n>c?0:n)}break;case l.TIME:case l.DATE:this.valueSync=String(e);break;default:{let a=Number(e);this.valueSync=a<0?0:a;break}}this.$nextTick(()=>{!g&&this._setValueArray()})},_setValueArray(){g=!0;var e=this.valueSync,t;switch(this.mode){case l.MULTISELECTOR:t=[...e];break;case l.TIME:t=this._getDateValueArray(e,w({mode:l.TIME}));break;case l.DATE:t=this._getDateValueArray(e,w({mode:l.DATE}));break;default:t=[e];break}this.oldValueArray=[...t],this.valueArray=[...t]},_getValue(){var e=this.valueArray;switch(this.mode){case l.SELECTOR:return e[0];case l.MULTISELECTOR:return e.map(t=>t);case l.TIME:return this.valueArray.map((t,a)=>this.timeArray[a][t]).join(":");case l.DATE:return this.valueArray.map((t,a)=>this.dateArray[a][t]).join("-")}},_getDateValueArray(e,t){let a=this.mode===l.DATE?"-":":",i=this.mode===l.DATE?this.dateArray:this.timeArray,r=3;switch(this.fields){case h.YEAR:r=1;break;case h.MONTH:r=2;break}let n=String(e).split(a),c=[];for(let o=0;o<r;o++){let u=n[o];c.push(i[o].indexOf(u))}return c.indexOf(-1)>=0&&(c=t?this._getDateValueArray(t):c.map(()=>0)),c},_change(){this.$emit("change",{value:this._getValue()})},_cancel(){this.$emit("cancel")},_pickerViewChange(e){this.valueArray=this._l10nColumn(e.detail.value,!0)},_l10nColumn(e,t){if(this.mode===l.DATE){let a=this.locale;if(!a.startsWith("zh"))switch(this.fields){case h.YEAR:return e;case h.MONTH:return[e[1],e[0]];default:switch(a){case"es":case"fr":return[e[2],e[1],e[0]];default:return t?[e[2],e[0],e[1]]:[e[1],e[2],e[0]]}}}return e},_l10nItem(e,t){if(this.mode===l.DATE){let a=this.locale;if(a.startsWith("zh"))return e+["\u5E74","\u6708","\u65E5"][t];if(this.fields!==h.YEAR&&t===(this.fields!==h.MONTH&&(a==="es"||a==="fr")?1:0)){let i;switch(a){case"es":i=["enero","febrero","marzo","abril","mayo","junio","\u200B\u200Bjulio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":i=["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"];break;default:i=["January","February","March","April","May","June","July","August","September","October","November","December"];break}return i[Number(e)-1]}}return e}}};function B(e,t,a,i,r,n){let c=(0,s.resolveComponent)("picker-view-column"),o=(0,s.resolveComponent)("picker-view");return(0,s.openBlock)(),(0,s.createElementBlock)("div",{class:(0,s.normalizeClass)(["content",{dark:e.isDark}])},[(0,s.createElementVNode)("div",{ref:"mask",style:(0,s.normalizeStyle)(n.maskStyle),class:"uni-mask",onClick:t[0]||(t[0]=(...u)=>n._cancel&&n._cancel(...u))},null,4),(0,s.createElementVNode)("div",{style:(0,s.normalizeStyle)(`padding-bottom:${e.safeAreaInsets.bottom}px;height:${r.height+e.safeAreaInsets.bottom}px;`),ref:"picker",class:(0,s.normalizeClass)(["uni-picker",{"uni-picker-dark":e.isDark}])},[(0,s.createElementVNode)("div",{class:(0,s.normalizeClass)(["uni-picker-header",{"uni-picker-header-dark":e.isDark}])},[(0,s.createElementVNode)("u-text",{style:(0,s.normalizeStyle)(`left:${e.safeAreaInsets.left}px`),class:(0,s.normalizeClass)(["uni-picker-action uni-picker-action-cancel",{"uni-picker-action-cancel-dark":e.isDark}]),onClick:t[1]||(t[1]=(...u)=>n._cancel&&n._cancel(...u))},(0,s.toDisplayString)(e.localize("cancel")),7),(0,s.createElementVNode)("u-text",{style:(0,s.normalizeStyle)(`right:${e.safeAreaInsets.right}px`),class:"uni-picker-action uni-picker-action-confirm",onClick:t[2]||(t[2]=(...u)=>n._change&&n._change(...u))},(0,s.toDisplayString)(e.localize("done")),5)],2),a.visible?((0,s.openBlock)(),(0,s.createBlock)(o,{key:0,style:(0,s.normalizeStyle)(`margin-left:${e.safeAreaInsets.left}px`),height:"216","indicator-style":n.pickerViewIndicatorStyle,"mask-top-style":n.pickerViewMaskTopStyle,"mask-bottom-style":n.pickerViewMaskBottomStyle,value:n._l10nColumn(r.valueArray),class:"uni-picker-content",onChange:n._pickerViewChange},{default:(0,s.withCtx)(()=>[((0,s.openBlock)(!0),(0,s.createElementBlock)(s.Fragment,null,(0,s.renderList)(n._l10nColumn(n.rangeArray),(u,y)=>((0,s.openBlock)(),(0,s.createBlock)(c,{length:u.length,key:y},{default:(0,s.withCtx)(()=>[(0,s.createCommentVNode)(" iOS\u6E32\u67D3\u901F\u5EA6\u6709\u95EE\u9898\u4F7F\u7528\u5355\u4E2Atext\u4F18\u5316 "),(0,s.createElementVNode)("u-text",{class:"uni-picker-item",style:(0,s.normalizeStyle)(n.pickerViewColumnTextStyle)},(0,s.toDisplayString)(n.getTexts(u,y)),5),(0,s.createCommentVNode)(` <text v-for="(item,index) in range" :key="index" class="uni-picker-item uni-picker-item-line" :style="{fontSize: fontSize + 'px'}">{{ typeof item==='object'?item[rangeKey]||'':_l10nItem(item) }}</text> `)]),_:2},1032,["length"]))),128))]),_:1},8,["style","indicator-style","mask-top-style","mask-bottom-style","value","onChange"])):(0,s.createCommentVNode)("v-if",!0)],6)],2)}var j=m(R,[["render",B],["styles",[Y]]]),W={page:{"":{flex:1}}},H={mixins:[k],components:{picker:j},data(){return{range:[],rangeKey:"",value:0,mode:"selector",fields:"day",start:"",end:"",disabled:!1,visible:!1}},onLoad(){this.data===null?this.postMessage({event:"created"},!0):this.showPicker(this.data),this.onMessage(e=>{this.showPicker(e)})},onReady(){this.$nextTick(()=>{this.visible=!0})},methods:{showPicker(e={}){let t=e.column;for(let a in e)a!=="column"&&(typeof t=="number"?this.$set(this.$data[a],t,e[a]):this.$data[a]=e[a])},close(e,{value:t=-1}={}){this.visible=!1,setTimeout(()=>{this.postMessage({event:e,value:t})},210)},onClose(){this.close("cancel")},columnchange({column:e,value:t}){this.$set(this.value,e,t),this.postMessage({event:"columnchange",column:e,value:t},!0)}}};function J(e,t,a,i,r,n){let c=(0,s.resolveComponent)("picker");return(0,s.openBlock)(),(0,s.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,s.createElementVNode)("view",{class:"page"},[(0,s.createVNode)(c,{range:r.range,rangeKey:r.rangeKey,value:r.value,mode:r.mode,fields:r.fields,start:r.start,end:r.end,disabled:r.disabled,visible:r.visible,onChange:t[0]||(t[0]=o=>n.close("change",o)),onCancel:t[1]||(t[1]=o=>n.close("cancel",o)),onColumnchange:n.columnchange},null,8,["range","rangeKey","value","mode","fields","start","end","disabled","visible","onColumnchange"])])])}var f=m(H,[["render",J],["styles",[W]]]);var p=plus.webview.currentWebview();if(p){let e=parseInt(p.id),t="template/__uniapppicker",a={};try{a=JSON.parse(p.__query__)}catch(r){}f.mpType="page";let i=Vue.createPageApp(f,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:t,__pageQuery:a});i.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...f.styles||[]])),i.mount("#root")}})();
