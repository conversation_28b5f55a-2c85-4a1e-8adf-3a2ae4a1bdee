<!--
 * @Description: 游戏规则弹窗组件
-->
<template>
  <view v-if="visible" class="modal-overlay" @click="closeModal">
    <view class="modal-container" @click.stop>
      <!-- 标题栏 -->
      <view class="modal-header">
        <text class="modal-title">🎲 游戏规则</text>
        <button class="close-btn" @click="closeModal">✕</button>
      </view>

      <!-- 规则内容 -->
      <scroll-view class="modal-content" scroll-y="true">
        <view class="rule-section">
          <text class="section-title">🎲 基本玩法</text>
          <text class="rule-text">选择啤酒类型，支付游戏费用，摇骰子获得相应重量的啤酒</text>
        </view>

        <view class="rule-section">
          <text class="section-title">🍺 啤酒类型</text>
          <view class="beer-info">
            <text class="beer-item">原浆：游戏费用 ¥10/次</text>
            <text class="beer-item">果啤：游戏费用 ¥15/次</text>
          </view>
        </view>

        <view class="rule-section">
          <text class="section-title">🎯 点数类型与奖励</text>
          <view class="dice-types">
            <view class="type-item">
              <text class="type-name">散花</text>
              <text class="type-desc">三个不同点数</text>
              <text class="type-reward">奖励：1斤啤酒</text>
            </view>
            <view class="type-item">
              <text class="type-name">对子</text>
              <text class="type-desc">两个相同点数</text>
              <text class="type-reward">奖励：2斤啤酒</text>
            </view>
            <view class="type-item">
              <text class="type-name">顺子</text>
              <text class="type-desc">连续三个点数</text>
              <text class="type-reward">奖励：3斤啤酒</text>
            </view>
            <view class="type-item">
              <text class="type-name">豹子</text>
              <text class="type-desc">三个相同点数</text>
              <text class="type-reward">奖励：4斤啤酒</text>
            </view>
          </view>
        </view>

        <view class="rule-section">
          <text class="section-title">💰 盈利计算</text>
          <text class="rule-text">收入 = 游戏费用（固定）</text>
          <text class="rule-text">成本 = 奖励重量 × 每斤成本</text>
          <text class="rule-text">盈利 = 收入 - 成本</text>
          <view class="example">
            <text class="example-title">示例：</text>
            <text class="example-text">客户选择原浆，摇出对子</text>
            <text class="example-text">收入：¥10（游戏费用）</text>
            <text class="example-text">成本：2斤 × ¥3.75 = ¥7.5</text>
            <text class="example-text">盈利：¥10 - ¥7.5 = ¥2.5</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义 emits
const emit = defineEmits(['close']);

// 关闭弹窗
const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
/* 手机端默认样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 30rpx;
}

.modal-container {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 650rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28rpx;
  color: #999;
  padding: 8rpx;
  border-radius: 50%;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #666;
}

.modal-cont
.rule-section {
  margin-bottom: 30rpx;
  padding: 25rpx 10rpx;
  background: #f8f9fa;

  border-radius: 15rpx;
  width:calc(750rpx - 40rpx)
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.rule-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

/* 啤酒信息 */
.beer-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.beer-item {
  font-size: 26rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 15rpx;
  border-radius: 10rpx;
  display: block;
}

/* 点数类型 */
.dice-types {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.type-item {
  background: rgba(255, 107, 107, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  border-left: 6rpx solid #ff6b6b;
}

.type-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.type-reward {
  font-size: 24rpx;
  color: #27ae60;
  font-weight: bold;
  display: block;
}

/* 示例 */
.example {
  background: rgba(39, 174, 96, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.example-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #27ae60;
  display: block;
  margin-bottom: 10rpx;
}

.example-text {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

/* 平板适配 */
@media screen and (min-width: 768px) {
  .modal-overlay {
    padding: 60rpx;
  }
  
  .modal-container {
    max-width: 800rpx;
  }
  
  .modal-header {
    padding: 40rpx;
  }
  
  .modal-title {
    font-size: 40rpx;
  }
  
  .close-btn {
    font-size: 36rpx;
    width: 70rpx;
    height: 70rpx;
  }
  
  .modal-content {
    padding: 40rpx;
  }
  
  .rule-section {
    padding: 30rpx 25rpx;
  }
  
  .section-title {
    font-size: 32rpx;
  }
  
  .rule-text {
    font-size: 28rpx;
  }
  
  .dice-types {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

/* PC端适配 */
@media screen and (min-width: 1024px) {
  .modal-overlay {
    padding: 80rpx;
  }
  
  .modal-container {
    max-width: 900rpx;
  }
  
  .modal-header {
    padding: 50rpx;
  }
  
  .modal-title {
    font-size: 44rpx;
  }
  
  .close-btn {
    font-size: 40rpx;
    width: 80rpx;
    height: 80rpx;
    cursor: pointer;
  }
  
  .modal-content {
    padding: 50rpx;
  }
  
  .rule-section {
    padding: 35rpx 30rpx;
  }
  
  .section-title {
    font-size: 36rpx;
  }
  
  .rule-text {
    font-size: 30rpx;
  }
}
</style>