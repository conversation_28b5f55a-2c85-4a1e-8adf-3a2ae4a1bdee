<template>  <view v-if="visible" class="modal-overlay" :class="{ 'fullscreen': fullscreen }" @click="handleOverlayClick">
    <view class="modal-container" :class="{ 'fullscreen': fullscreen }" @click.stop>
      <view class="modal-header">
        <text class="modal-title">📊 记录分析</text>
        <button class="close-btn" @click="$emit('close')">✕</button>
      </view>
      <view class="modal-content">
        <view class="analysis-content">
          <!-- 第一行：总体统计 -->
          <view class="stats-row" data-title="📊 总体统计">
            <view class="stat-card">
              <text class="stat-label">总收入</text>
              <text class="stat-value revenue">{{ formatCurrency(totalRevenue) }}</text>
            </view>
            <view class="stat-card">
              <text class="stat-label">总成本</text>
              <text class="stat-value cost">{{ formatCurrency(totalCost) }}</text>
            </view>
            <view class="stat-card">
              <text class="stat-label">总盈利</text>
              <text class="stat-value profit" :class="{ negative: totalProfit < 0 }">
                {{ formatCurrency(totalProfit) }}
              </text>
            </view>
            <view class="stat-card">
              <text class="stat-label">毛润率</text>
              <text class="stat-value margin" :class="{
                good: totalMargin >= 40,
                warning: totalMargin >= 20 && totalMargin < 40,
                danger: totalMargin < 20
              }">
                {{ formatPercentage(totalMargin) }}%
              </text>
            </view>
          </view>

          <!-- 第二行：啤酒类型统计 -->
          <view class="stats-row" data-title="🍺 啤酒类型统计">
            <!-- 原浆啤酒卡片 -->
            <view class="stat-card beer-draft">
              <view class="beer-stat-row">
                <view class="beer-stat-item">
                  <text class="stat-label">原浆收入</text>
                  <text class="stat-value">{{ formatCurrency(yuanJiangRevenue) }}</text>
                </view>
                <view class="beer-stat-item">
                  <text class="stat-label">原浆成本</text>
                  <text class="stat-value">{{ formatCurrency(yuanJiangCost) }}</text>
                </view>
              </view>
              <view class="beer-stat-row">
                <view class="beer-stat-item">
                  <text class="stat-label">原浆盈利</text>
                  <text class="stat-value">{{ formatCurrency(yuanJiangProfit) }}</text>
                </view>
                <view class="beer-stat-item">
                  <text class="stat-label">原浆毛润</text>
                  <text class="stat-value">{{ formatPercentage(yuanJiangMargin) }}%</text>
                </view>
              </view>
            </view>
            
            <!-- 果啤卡片 -->
            <view class="stat-card beer-fruit">
              <view class="beer-stat-row">
                <view class="beer-stat-item">
                  <text class="stat-label">果啤收入</text>
                  <text class="stat-value">{{ formatCurrency(guoPiRevenue) }}</text>
                </view>
                <view class="beer-stat-item">
                  <text class="stat-label">果啤成本</text>
                  <text class="stat-value">{{ formatCurrency(guoPiCost) }}</text>
                </view>
              </view>
              <view class="beer-stat-row">
                <view class="beer-stat-item">
                  <text class="stat-label">果啤盈利</text>
                  <text class="stat-value">{{ formatCurrency(guoPiProfit) }}</text>
                </view>
                <view class="beer-stat-item">
                  <text class="stat-label">果啤毛润</text>
                  <text class="stat-value">{{ formatPercentage(guoPiMargin) }}%</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 第三行：点数类型统计 -->
          <view class="stats-row" data-title="🎲 点数类型统计">
            <!-- 散花卡片 -->
            <view class="stat-card dice-sanhua">
              <view class="dice-type-title">散花</view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">收入</text>
                  <text class="stat-value">{{ formatCurrency(sanHuaRevenue) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">成本</text>
                  <text class="stat-value">{{ formatCurrency(sanHuaCost) }}</text>
                </view>
              </view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">盈利</text>
                  <text class="stat-value">{{ formatCurrency(sanHuaProfit) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">利润率</text>
                  <text class="stat-value">{{ formatPercentage(sanHuaMargin) }}%</text>
                </view>
              </view>
            </view>
            
            <!-- 对子卡片 -->
            <view class="stat-card dice-duizi">
              <view class="dice-type-title">对子</view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">收入</text>
                  <text class="stat-value">{{ formatCurrency(duiZiRevenue) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">成本</text>
                  <text class="stat-value">{{ formatCurrency(duiZiCost) }}</text>
                </view>
              </view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">盈利</text>
                  <text class="stat-value">{{ formatCurrency(duiZiProfit) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">利润率</text>
                  <text class="stat-value">{{ formatPercentage(duiZiMargin) }}%</text>
                </view>
              </view>
            </view>
            
            <!-- 顺子卡片 -->
            <view class="stat-card dice-shunzi">
              <view class="dice-type-title">顺子</view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">收入</text>
                  <text class="stat-value">{{ formatCurrency(shunZiRevenue) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">成本</text>
                  <text class="stat-value">{{ formatCurrency(shunZiCost) }}</text>
                </view>
              </view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">盈利</text>
                  <text class="stat-value">{{ formatCurrency(shunZiProfit) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">利润率</text>
                  <text class="stat-value">{{ formatPercentage(shunZiMargin) }}%</text>
                </view>
              </view>
            </view>
            
            <!-- 豹子卡片 -->
            <view class="stat-card dice-baozi">
              <view class="dice-type-title">豹子</view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">收入</text>
                  <text class="stat-value">{{ formatCurrency(baoZiRevenue) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">成本</text>
                  <text class="stat-value">{{ formatCurrency(baoZiCost) }}</text>
                </view>
              </view>
              <view class="dice-stat-row">
                <view class="dice-stat-item">
                  <text class="stat-label">盈利</text>
                  <text class="stat-value">{{ formatCurrency(baoZiProfit) }}</text>
                </view>
                <view class="dice-stat-item">
                  <text class="stat-label">利润率</text>
                  <text class="stat-value">{{ formatPercentage(baoZiMargin) }}%</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 第四行：补充统计 -->
          <view class="stats-row" data-title="📈 补充统计">
            <view class="stat-card">
              <view class="supplement-stat-row">
                <view class="supplement-stat-item">
                  <text class="stat-label">游戏次数</text>
                  <text class="stat-value">{{ formatInteger(totalGames) }}</text>
                </view>
                <view class="supplement-stat-item">
                  <text class="stat-label">平均盈利</text>
                  <text class="stat-value">{{ formatCurrency(averageProfit) }}</text>
                </view>
                <view class="supplement-stat-item">
                  <text class="stat-label">总重量</text>
                  <text class="stat-value">{{ formatInteger(totalWeight) }}</text>
                </view>
                <view class="supplement-stat-item">
                  <text class="stat-label">平均每斤盈利</text>
                  <text class="stat-value">{{ formatCurrency(averageProfitPerJin) }}</text>
                </view>
              </view>

            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {
  BeverageType,
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatInteger,
  calcTotalRevenue,
  calcTotalCost,
  calcTotalProfit,
  calcTotalMargin,
  calcBeerTypeRevenue,
  calcBeerTypeCost,
  calcBeerTypeProfit,
  calcBeerTypeMargin,
  calcDiceTypeRevenue,
  calcDiceTypeCost,
  calcDiceTypeProfit,
  calcDiceTypeMargin,
  calcTotalGames,
  calcTotalWeight,
  calcAverageProfit,
  calcAverageProfitPerJin
} from '../utils/diceCalculator.js';

// 添加 fullscreen 属性支持
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  fullscreen: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['close']);

const handleOverlayClick = () => {
  // 点击遮罩层关闭弹窗
  emit('close');
};

// 响应式数据，面板打开时实时刷新
const refreshTrigger = ref(0);

// 监听面板可见性，打开时强制刷新数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    refreshTrigger.value++;
  }
});

// 总体统计数据 - 使用独立函数计算
const totalRevenue = computed(() => {
  refreshTrigger.value; // 触发响应式更新
  return calcTotalRevenue();
});

const totalCost = computed(() => {
  refreshTrigger.value;
  return calcTotalCost();
});

const totalProfit = computed(() => {
  refreshTrigger.value;
  return calcTotalProfit();
});

const totalMargin = computed(() => {
  refreshTrigger.value;
  return calcTotalMargin();
});

// 原浆数据
const yuanJiangRevenue = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeRevenue(BeverageType.YUAN_JIANG);
});

const yuanJiangCost = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeCost(BeverageType.YUAN_JIANG);
});

const yuanJiangProfit = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeProfit(BeverageType.YUAN_JIANG);
});

const yuanJiangMargin = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeMargin(BeverageType.YUAN_JIANG);
});

// 果啤数据
const guoPiRevenue = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeRevenue(BeverageType.GUO_PI);
});

const guoPiCost = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeCost(BeverageType.GUO_PI);
});

const guoPiProfit = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeProfit(BeverageType.GUO_PI);
});

const guoPiMargin = computed(() => {
  refreshTrigger.value;
  return calcBeerTypeMargin(BeverageType.GUO_PI);
});

// 点数类型数据
const sanHuaRevenue = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeRevenue('散花');
});

const sanHuaCost = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeCost('散花');
});

const sanHuaProfit = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeProfit('散花');
});

const sanHuaMargin = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeMargin('散花');
});

const duiZiRevenue = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeRevenue('对子');
});

const duiZiCost = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeCost('对子');
});

const duiZiProfit = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeProfit('对子');
});

const duiZiMargin = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeMargin('对子');
});

const shunZiRevenue = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeRevenue('顺子');
});

const shunZiCost = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeCost('顺子');
});

const shunZiProfit = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeProfit('顺子');
});

const shunZiMargin = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeMargin('顺子');
});

const baoZiRevenue = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeRevenue('豹子');
});

const baoZiCost = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeCost('豹子');
});

const baoZiProfit = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeProfit('豹子');
});

const baoZiMargin = computed(() => {
  refreshTrigger.value;
  return calcDiceTypeMargin('豹子');
});

// 补充统计数据
const totalGames = computed(() => {
  refreshTrigger.value;
  return calcTotalGames();
});

const totalWeight = computed(() => {
  refreshTrigger.value;
  return calcTotalWeight();
});

const averageProfit = computed(() => {
  refreshTrigger.value;
  return calcAverageProfit();
});

const averageProfitPerJin = computed(() => {
  refreshTrigger.value;
  return calcAverageProfitPerJin();
});

</script>

<style scoped>
/* 遮罩层基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20rpx;
}

/* 全屏模式下的遮罩层样式 */
.modal-overlay.fullscreen {
  padding: 0;
}

/* 弹窗容器基础样式 */
.modal-container {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 800rpx;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 全屏模式下的弹窗容器样式 */
.modal-container.fullscreen {
  max-width: none;
  max-height: none;
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
}

/* 弹窗头部样式 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rpx 15rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  flex-shrink: 0;
}

/* 弹窗标题样式 */
.modal-title {
  font-size: 16rpx;
  font-weight: bold;
  color: white;
  flex: 1;
  height: 35rpx;
  line-height: 35rpx;
}

/* 关闭按钮样式 */
.close-btn {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}


/* 弹窗内容区域样式 */
.modal-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
}



/* 分析内容容器样式 */
.analysis-content {
  padding: 10rpx 12rpx 5rpx 5rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);


  gap: 0;
}

/* 全屏模式下的分析内容容器样式 */
.analysis-content.fullscreen {
  padding: 10rpx;
  height: calc(100vh - 80rpx);
}

/* 统计行容器样式 */
.stats-row {
  display: flex;
  gap: 4rpx;
  margin-bottom: 0;
  margin-top: 4rpx;
  flex-wrap: wrap;
  width: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6rpx;
  padding: 4rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 第一行不需要上边距 */
.stats-row:first-child {
  margin-top: 0;
}


/* 最后一行统计行的样式（移除底部边距） */
.stats-row:last-child {
  margin-bottom: 0;
}

/* 统计卡片样式 */
.stat-card {
  background: rgba(255, 255, 255, 0.5);
  padding: 8rpx 6rpx;
  border-radius: 8rpx;
  text-align: center;
  flex: 1;
  min-width: 0;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-2rpx);
}

/* 第一行统计卡片特殊样式 */
.stats-row:first-child .stat-card {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 4rpx 3rpx;
}

/* 啤酒统计行样式 */
.beer-stat-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 6rpx;
  width: 100%;
  gap: 4rpx;
}

.beer-stat-row:last-child {
  margin-bottom: 0;
}

/* 啤酒统计项样式 */
.beer-stat-item {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 5rpx 6rpx;
  margin: 0;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.beer-stat-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.02);
}

/* 点数类型标题样式 */
.dice-type-title {
  font-size: 12rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 3rpx;
  padding: 2rpx 0;
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.3);
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 2rpx;
}

/* 点数统计行样式 */
.dice-stat-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 4rpx;
  width: 100%;
  gap: 3rpx;
}

.dice-stat-row:last-child {
  margin-bottom: 0;
}

/* 点数统计项样式 */
.dice-stat-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 4rpx 3rpx;
  margin: 0;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-height: 30rpx;
}

.dice-stat-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

/* 补充统计行样式 */
.supplement-stat-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0;
  width: 100%;
  gap: 5rpx;
}

/* 补充统计项样式 */
.supplement-stat-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 6rpx 4rpx;
  margin: 0;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-height: 35rpx;
}

.supplement-stat-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1rpx);
}

/* 统计标签文字样式 */
.stat-label {
  font-size: 14rpx;
  color: #666;
  display: block;
  margin-bottom: 2rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  font-weight: 500;
}

/* 统计值文字样式 */
.stat-value {
  font-size: 16rpx;
  font-weight: bold;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  flex: 1;
  margin-left: 4rpx;
}

/* 收入数值样式 */
.stat-value.revenue {
  color: #27ae60;
}

/* 成本数值样式 */
.stat-value.cost {
  color: #e74c3c;
}

/* 盈利数值样式 */
.stat-value.profit {
  color: #27ae60;
}

/* 负盈利数值样式 */
.stat-value.profit.negative {
  color: #e74c3c;
}

/* 高利润率样式 */
.stat-value.margin.good {
  color: #27ae60;
}

/* 中等利润率样式 */
.stat-value.margin.warning {
  color: #f39c12;
}

/* 低利润率样式 */
.stat-value.margin.danger {
  color: #e74c3c;
}

/* 原浆啤酒卡片样式 */
.stat-card.beer-draft {
  background: linear-gradient(135deg, #fff5f2 0%, #ffe8e0 100%);
}

.stat-card.beer-draft::before {
  background: linear-gradient(90deg, #ff6b35 0%, #ff8a65 100%);
}

/* 果啤卡片样式 */
.stat-card.beer-fruit {
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.stat-card.beer-fruit::before {
  background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

/* 散花点数卡片样式 */
.stat-card.dice-sanhua {
  background: linear-gradient(135deg, #e3f2fd 0%, #e1f5fe 100%);
}

.stat-card.dice-sanhua::before {
  background: linear-gradient(90deg, #2196f3 0%, #42a5f5 100%);
}

/* 对子点数卡片样式 */
.stat-card.dice-duizi {
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
}

.stat-card.dice-duizi::before {
  background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

/* 顺子点数卡片样式 */
.stat-card.dice-shunzi {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.stat-card.dice-shunzi::before {
  background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
}

/* 豹子点数卡片样式 */
.stat-card.dice-baozi {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
}

.stat-card.dice-baozi::before {
  background: linear-gradient(90deg, #e91e63 0%, #f06292 100%);
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  /* 移动端遮罩层样式 */
  .modal-overlay {
    padding: 5rpx;
  }

  /* 移动端全屏模式下的遮罩层样式 */
  .modal-overlay.fullscreen {
    padding: 0;
  }

  /* 移动端弹窗容器样式 */
  .modal-container {
    max-height: 90vh;
  }

  /* 移动端全屏模式下的弹窗容器样式 */
  .modal-container.fullscreen {
    max-height: none;
    height: 100vh;
  }

  /* 移动端弹窗头部样式 */
  .modal-header {
    padding: 1rpx 5rpx;
  }

  /* 移动端弹窗标题样式 */
  .modal-title {
    font-size: 14rpx;
  }

  /* 移动端关闭按钮样式 */
  .close-btn {
    width: 15rpx;
    height: 15rpx;
    font-size: 10rpx;
  }

  /* 移动端分析内容容器样式 */
  .analysis-content {
    padding: 5rpx;
  }

  /* 移动端全屏模式下的分析内容容器样式 */
  .analysis-content.fullscreen {
    padding: 5rpx;
    height: calc(100vh - 60rpx);
  }

  /* 移动端统计行容器样式 */
  .stats-row {
    gap: 2rpx;
    margin-top: 2rpx;
    border-radius: 4rpx;
    padding: 2rpx;
    background: rgba(255, 255, 255, 0.5);
  }

  /* 移动端统计卡片样式 */
  .stat-card {
    padding: 4rpx 3rpx;
    border-radius: 6rpx;
    background: rgba(255, 255, 255, 0.5);
  }

  /* 移动端统计行上边距 */
  .stats-row {
    margin-top: 3rpx;
  }

  /* 移动端统计标签文字样式 */
  .stat-label {
    font-size: 12rpx;
  }

  /* 移动端统计值文字样式 */
  .stat-value {
    font-size: 14rpx;
  }

  /* 移动端补充统计行样式 */
  .supplement-stat-row {
    gap: 3rpx;
  }

  /* 移动端补充统计项样式 */
  .supplement-stat-item {
    padding: 3rpx 2rpx;
    border-radius: 2rpx;
    min-height: 20rpx;
  }

  /* 移动端点数类型标题 */
  .dice-type-title {
    font-size: 10rpx;
  }
}

/* 平板端适配 */
@media screen and (min-width: 600px) {
  /* 平板端全屏模式下的弹窗容器样式 */
  .modal-container.fullscreen {
    width: 100vw;
    height: 100vh;
  }
  
  /* 平板端全屏模式下的分析内容容器样式 */
  .analysis-content.fullscreen {
    padding: 8rpx;
    height: calc(100vh - 100rpx);
  }
  
  /* 平板端统计行容器样式 */
  .stats-row {
    gap: 3rpx;
    margin-top: 3rpx;
    border-radius: 5rpx;
    padding: 3rpx;
    background: rgba(255, 255, 255, 0.5);
  }

  /* 平板端统计标签文字样式 */
  .stat-label {
    font-size: 16rpx;
  }

  /* 平板端统计值文字样式 */
  .stat-value {
    font-size: 18rpx;
  }

  /* 平板端补充统计行样式 */
  .supplement-stat-row {
    gap: 4rpx;
  }

  /* 平板端补充统计项样式 */
  .supplement-stat-item {
    padding: 4rpx 3rpx;
    border-radius: 3rpx;
    min-height: 25rpx;
  }

  /* 平板端统计行上边距 */
  .stats-row {
    margin-top: 11rpx;
  }
  
  /* 平板端点数类型标题 */
  .dice-type-title {
    font-size: 14rpx;
  }
}

/* PC端适配 */
@media screen and (min-width: 1024px) {
  /* PC端遮罩层样式 */
  .modal-overlay {
    padding: 30rpx;
  }
  
  /* PC端全屏模式下的遮罩层样式 */
  .modal-overlay.fullscreen {
    padding: 0;
  }

  /* PC端弹窗容器样式 */
  .modal-container {
    max-width: 900rpx;
  }
  
  /* PC端全屏模式下的弹窗容器样式 */
  .modal-container.fullscreen {
    max-width: none;
    width: 100vw;
    height: 100vh;
  }
  
  /* PC端弹窗头部样式 */
  .modal-header {
    padding: 10rpx 40rpx;
  }
  
  /* PC端弹窗标题样式 */
  .modal-title {
    font-size: 36rpx;
  }
  
  /* PC端分析内容容器样式 */
  .analysis-content {
    padding: 10rpx;
  }
  
  /* PC端全屏模式下的分析内容容器样式 */
  .analysis-content.fullscreen {
    padding: 10rpx;
    height: calc(100vh - 150rpx);
  }
  
  /* PC端统计行容器样式 */
  .stats-row {
    gap: 4rpx;
    margin-top: 4rpx;
    border-radius: 6rpx;
    padding: 4rpx;
    background: rgba(255, 255, 255, 0.5);
  }

  /* PC端统计标签文字样式 */
  .stat-label {
    font-size: 26rpx;
  }

  /* PC端统计值文字样式 */
  .stat-value {
    font-size: 30rpx;
  }

  /* PC端补充统计行样式 */
  .supplement-stat-row {
    gap: 5rpx;
  }

  /* PC端补充统计项样式 */
  .supplement-stat-item {
    padding: 6rpx 4rpx;
    border-radius: 4rpx;
    min-height: 35rpx;
  }

  /* PC端统计行上边距 */
  .stats-row {
    margin-top: 6rpx;
  }

  .stats-row:first-child {
    margin-top: 0;
  }

  /* PC端点数类型标题 */
  .dice-type-title {
    font-size: 16rpx;
  }
}
</style>