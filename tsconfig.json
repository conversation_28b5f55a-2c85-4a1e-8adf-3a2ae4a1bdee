{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "allowJs": true,
    
    /* Bundler mode */
    "moduleResolution": "node",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    
    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    
    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  },
  "vueCompilerOptions": {
    "target": 3
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.vue",
    "**/*.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "unpackage"
  ]
}