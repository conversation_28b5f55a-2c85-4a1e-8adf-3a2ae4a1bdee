/* ================================= 移动端样式 (默认) ================================= */
.game-container {
  min-height: 100vh;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "middle"
    "bottom";
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  gap: 20rpx;
}

/* 上部：标题栏 */
.header-area {
  background: rgba(255, 255, 255, 0.1);
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-radius: 15rpx;
}

.rules-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  flex-shrink: 0;
}

.header-buttons {
  display: flex;
  gap: 15rpx;
  flex-shrink: 0;
}

.header-btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2rpx);
}

.title-section {
  text-align: center;
  flex: 1;
}

.main-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.sub-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 中部：三栏布局 */
.middle-area {
  background: rgba(255, 255, 255, 0.05);
  grid-area: middle;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "rules"
    "game"
    "history";
  gap: 15rpx;
  padding: 20rpx;
  border-radius: 15rpx;
}

/* 左：游戏规则面板 */
.rules-panel {
  background: rgba(255, 255, 255, 0.1);
  grid-area: rules;
  padding: 20rpx;
  border-radius: 10rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.panel-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  display: block;
}

.record-count {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

.action-btn {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clear-btn {
  background: rgba(255, 107, 107, 0.8);
  color: white;
}

.clear-btn:hover {
  background: rgba(255, 107, 107, 1);
  transform: translateY(-1rpx);
}

.analysis-btn {
  background: rgba(103, 126, 234, 0.8);
  color: white;
}

.analysis-btn:hover {
  background: rgba(103, 126, 234, 1);
  transform: translateY(-1rpx);
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex: 1;
}

.rule-section {
  margin-bottom: 15rpx;
}

.section-subtitle {
  font-size: 20rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
  display: block;
  margin-bottom: 8rpx;
  text-align: center;
}

.rule-list {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.rule-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 6rpx;
  padding: 8rpx 6rpx;
  margin-bottom: 4rpx;
}

.rule-item {
  font-size: 18rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 2rpx;
}

.rule-desc {
  font-size: 14rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-bottom: 2rpx;
  text-align: center;
}

.rule-reward {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  display: block;
  text-align: center;
}

.rule-extra {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  text-align: center;
  margin-bottom: 4rpx;
  background: rgba(255, 255, 255, 0.05);
  padding: 6rpx;
  border-radius: 4rpx;
}

/* 游戏规则字体颜色 - 基于紫色渐变背景优化 */
.rule-sanhua {
  color: #4FC3F7 !important;
  /* 散花 - 天蓝色，与豹子粉色形成明显区分 */
}

.rule-duizi {
  color: #81C784 !important;
  /* 对子 - 清新绿色，与紫色形成互补 */
}

.rule-shunzi {
  color: #FFB74D !important;
  /* 顺子 - 温暖橙色，增加活力 */
}

.rule-baozi {
  color: #F48FB1 !important;
  /* 豹子 - 粉红色，保持温和但突出 */
}

/* 中：游戏区域 */
.game-area {
  background: rgba(255, 255, 255, 0.08);
  grid-area: game;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  border-radius: 10rpx;
  min-height: 300rpx; /* 确保游戏区域有最小高度 */
}

.dice-container {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  align-items: center;
  justify-content: center;
  min-height: 120rpx; /* 确保有足够高度显示骰子 */
  padding: 20rpx 0; /* 添加上下内边距 */
}

/* 确保骰子之间有间距 */
.dice-container > * {
  margin: 0 40rpx;
}

.dice-container > *:first-child {
  margin-left: 0;
}

.dice-container > *:last-child {
  margin-right: 0;
}

.game-result {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 10rpx;
  width: 90%; /* 调整为90%宽度，留出更多空间 */
  max-width: 500rpx; /* 限制最大宽度 */
  margin: 20rpx auto 0; /* 居中显示 */
  min-height: 60rpx; /* 固定最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.result-content {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10rpx; /* 添加间距 */
  height: auto; /* 自适应高度 */
}

.result-prompt {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
  line-height: 1.2;
}

.result-rolling {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffa500;
  display: block;
  line-height: 1.2;
  animation: rolling-pulse 1s ease-in-out infinite;
}

@keyframes rolling-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.dice-display {
  margin-bottom: 0;
}

.dice-numbers {
  font-size: 18rpx;
  font-weight: bold;
  color: #333;
  background: rgba(0, 0, 0, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 15rpx;
}

.result-type {
  font-size: 18rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 0;
}

/* 游戏结果类型颜色 - 与游戏规则保持一致 */
.result-sanhua {
  color: #4FC3F7 !important; /* 散花 - 天蓝色 */
}

.result-duizi {
  color: #81C784 !important; /* 对子 - 清新绿色 */
}

.result-shunzi {
  color: #FFB74D !important; /* 顺子 - 温暖橙色 */
}

.result-baozi {
  color: #F48FB1 !important; /* 豹子 - 粉红色 */
}

.result-weight,
.result-beer {
  font-size: 20rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.result-profit {
  font-size: 22rpx;
  font-weight: bold;
  color: #27ae60;
  display: block;
}

/* 右：游戏记录面板 */
.history-panel {
  background: rgba(255, 255, 255, 0.1);
  grid-area: history;
  padding: 20rpx;
  border-radius: 10rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-content {
  flex: 1;
  overflow-y: auto;
}

.no-history {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 22rpx;
  padding: 20rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx;
}

.history-dice-container {
  display: flex;
  gap: 4rpx;
  align-items: center;
}

.history-dice-single {
  width: 24rpx;
  height: 24rpx;
  background: white;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.history-dice-number {
  font-size: 14rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.history-type {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

/* 游戏记录背景颜色 - 与新颜色方案协调 */
.history-sanhua {
  background: linear-gradient(135deg, #4FC3F7 0%, #2196F3 100%) !important;
  /* 散花 - 蓝色渐变背景 */
}

.history-sanhua .history-type {
  color: white !important;
  /* 白色字体在紫色背景上 */
}

.history-duizi {
  background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%) !important;
  /* 对子 - 绿色渐变背景 */
}

.history-duizi .history-type {
  color: white !important;
  /* 白色字体在绿色背景上 */
}

.history-shunzi {
  background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%) !important;
  /* 顺子 - 橙色渐变背景 */
}

.history-shunzi .history-type {
  color: white !important;
  /* 白色字体在橙色背景上 */
}

.history-baozi {
  background: linear-gradient(135deg, #F48FB1 0%, #E91E63 100%) !important;
  /* 豹子 - 粉色渐变背景 */
}

.history-baozi .history-type {
  color: white !important;
  /* 白色字体在粉色背景上 */
}

/* 下部：啤酒选择按钮 */
.bottom-area {
  background: rgba(255, 255, 255, 0.1);
  grid-area: bottom;
  position: relative;
  padding: 25rpx;
  border-radius: 15rpx;
}

.bottom-actions {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  display: flex;
  gap: 8rpx;
  z-index: 10;
}

.beer-buttons {
  display: grid;
  grid-template-columns: 1fr 3fr 2fr 3fr 1fr;
  gap: 0;
  width: 100%;
}

.beer-btn {
  padding: 0;
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 26rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 25rpx 20rpx;
  height: 100%;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0rpx;
  line-height: 1;
}

.btn-title {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
  margin-bottom: -2rpx;
}

.btn-price {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: normal;
  line-height: 1;
}

.beer-buttons .draft-btn {
  grid-column: 2;
}

.beer-buttons .fruit-btn {
  grid-column: 4;
}

.beer-btn.active {
  border-color: rgba(255, 255, 255, 0.8);
}

.draft-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-4rpx);
}

.draft-btn.active .btn-title {
  color: white;
}

.draft-btn.active .btn-price {
  color: rgba(255, 255, 255, 0.9);
}

.fruit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-4rpx);
}

.fruit-btn.active .btn-title {
  color: white;
}

.fruit-btn.active .btn-price {
  color: rgba(255, 255, 255, 0.9);
}

/* 按钮禁用状态 */
.beer-btn.disabled,
.beer-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  transform: none !important;
  box-shadow: none !important;
}

/* ================================= 平板样式 (600px+) ================================= */
@media screen and (min-width: 600px) {
  .game-container {
    height: 100vh;
    display: grid;
    grid-template-rows: auto 1fr auto;
    grid-template-areas:
      "header"
      "middle"
      "bottom";
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 10rpx;
    gap: 10rpx;
    box-sizing: border-box;
    overflow: hidden;
  }

  /* 上部：标题栏 */
  .header-area {
    background: rgba(255, 255, 255, 0.1);
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15rpx 20rpx;
    border-radius: 10rpx;
    box-sizing: border-box;
    min-height: 60rpx;
  }

  .rules-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8rpx 16rpx;
    border-radius: 15rpx;
    font-size: 16rpx;
    cursor: pointer;
    white-space: nowrap;
    min-width: 80rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .rules-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1rpx);
  }

  .header-buttons {
    display: flex;
    gap: 8rpx;
    flex-shrink: 0;
  }

  .header-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    font-size: 14rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1rpx);
  }

  .title-section {
    text-align: center;
    flex: 1;
    margin: 0 15rpx;
  }

  .main-title {
    font-size: 20rpx;
    font-weight: bold;
    color: white;
    display: block;
    margin-bottom: 2rpx;
    line-height: 1.1;
  }

  .sub-title {
    font-size: 14rpx;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    line-height: 1.1;
  }

  /* 中部：三栏布局 */
  .middle-area {
    background: rgba(255, 255, 255, 0.05);
    grid-area: middle;
    display: grid;
    grid-template-columns: 120rpx 1fr 120rpx;
    grid-template-rows: 1fr;
    grid-template-areas: "rules game history";
    gap: 10rpx;
    padding: 10rpx;
    border-radius: 10rpx;
    box-sizing: border-box;
    transition: grid-template-columns 0.3s ease;
    align-items: start; /* 改为顶部对齐，不拉伸 */
    max-height: 600rpx; /* 限制中间区域最大高度 */
    overflow: hidden;
  }

  /* 隐藏规则面板时的布局 */
  .middle-area.hide-rules {
    grid-template-columns: 0 1fr 120rpx;
  }

  /* 隐藏历史面板时的布局 */
  .middle-area.hide-history {
    grid-template-columns: 120rpx 1fr 0;
  }

  /* 隐藏两个面板时的布局 */
  .middle-area.hide-both {
    grid-template-columns: 0 1fr 0;
  }

  /* 左：游戏规则面板 */
  .rules-panel {
    background: rgba(255, 255, 255, 0.1);
    grid-area: rules;
    padding: 10rpx;
    border-radius: 8rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: auto; /* 改为自动高度 */
    min-height: 300rpx; /* 设置最小高度 */
    max-height: 400rpx; /* 限制最大高度，与其他面板一致 */
  }

  .panel-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .panel-title {
    font-size: 16rpx;
    font-weight: bold;
    color: white;
    display: block;
    text-align: center;
  }

  .record-count {
    font-size: 12rpx;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    text-align: center;
  }

  .action-btn {
    padding: 3rpx 6rpx;
    border-radius: 6rpx;
    font-size: 10rpx;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
  }

  .clear-btn {
    background: rgba(255, 107, 107, 0.8);
    color: white;
  }

  .clear-btn:hover {
    background: rgba(255, 107, 107, 1);
  }

  .analysis-btn {
    background: rgba(103, 126, 234, 0.8);
    color: white;
  }

  .analysis-btn:hover {
    background: rgba(103, 126, 234, 1);
  }

  .rules-content {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 320rpx; /* 与历史面板保持一致 */
    padding-right: 5rpx; /* 为滚动条留出空间 */
  }

  .rule-section {
    margin-bottom: 10rpx;
  }

  .section-subtitle {
    font-size: 12rpx;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.95);
    display: block;
    margin-bottom: 6rpx;
    text-align: center;
  }

  .rule-list {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }

  .rule-item-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 4rpx;
    padding: 4rpx 3rpx;
    margin-bottom: 2rpx;
  }

  .rule-item {
    font-size: 10rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 1rpx;
    line-height: 1.2;
  }

  .rule-desc {
    font-size: 8rpx;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    margin-bottom: 1rpx;
    text-align: center;
    line-height: 1.1;
  }

  .rule-reward {
    font-size: 9rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    display: block;
    text-align: center;
    line-height: 1.1;
  }

  .rule-extra {
    font-size: 8rpx;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    text-align: center;
    margin-bottom: 2rpx;
    background: rgba(255, 255, 255, 0.05);
    padding: 3rpx;
    border-radius: 2rpx;
    line-height: 1.2;
  }

  /* 平板端滚动条样式 - 规则面板 */
  .rules-content::-webkit-scrollbar {
    width: 4rpx;
  }

  .rules-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2rpx;
  }

  .rules-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2rpx;
  }

  .rules-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  /* 平板端游戏规则字体颜色 - 优化协调性 */
  .rule-sanhua {
    color: #29B6F6 !important;
    /* 散花 - 中等蓝色，与豹子粉色形成明显区分 */
  }

  .rule-duizi {
    color: #A5D6A7 !important;
    /* 对子 - 中等绿色，保持清新感 */
  }

  .rule-shunzi {
    color: #FFCC02 !important;
    /* 顺子 - 金黄色，更加醒目 */
  }

  .rule-baozi {
    color: #F8BBD9 !important;
    /* 豹子 - 浅粉色，温和而突出 */
  }

  /* 中：游戏区域 */
  .game-area {
    background: rgba(255, 255, 255, 0.08);
    grid-area: game;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15rpx;
    border-radius: 8rpx;
    box-sizing: border-box;
    height: auto; /* 自动高度 */
    min-height: 300rpx; /* 最小高度300rpx */
    max-height: 400rpx; /* 最大高度400rpx */
    overflow: hidden; /* 防止内容溢出 */
  }

  .dice-container {
    display: flex;
    gap: 15rpx;
    margin-bottom: 20rpx;
    align-items: center;
    justify-content: center;
    min-height: 80rpx; /* 平板端骰子容器高度 */
    max-height: 80rpx; /* 限制最大高度 */
  }

  /* 平板端确保骰子之间有间距 */
  .dice-container > * {
    margin: 0 18rpx;
  }

  .dice-container > *:first-child {
    margin-left: 0;
  }

  .dice-container > *:last-child {
    margin-right: 0;
  }

  .game-result {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 15rpx;
    border-radius: 8rpx;
    width: 85%; /* 调整为85%宽度 */
    max-width: 400rpx; /* 限制最大宽度 */
    margin: 15rpx auto 0; /* 居中显示 */
    min-height: 40rpx; /* 调整最小高度 */
    max-height: 60rpx; /* 调整最大高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* 防止内容溢出 */
    box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.12); /* 添加阴影效果 */
  }

  .result-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 30rpx; /* 平板端内容区域固定高度 */
    overflow: hidden; /* 防止内容溢出影响布局 */
  }

  .result-prompt {
    font-size: 18rpx;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    line-height: 1.1;
  }

  .result-rolling {
    font-size: 18rpx;
    font-weight: bold;
    color: #ffa500;
    display: block;
    line-height: 1.1;
    animation: rolling-pulse 1s ease-in-out infinite;
  }

  .dice-numbers {
    font-size: 18rpx;
    font-weight: bold;
    color: #333;
    background: rgba(0, 0, 0, 0.05);
    padding: 1rpx 6rpx;
    border-radius: 8rpx;
    margin-bottom: 2rpx;
  }

  .result-type {
    font-size: 18rpx;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    margin-bottom: 6rpx;
  }

  /* 平板端游戏结果类型颜色 - 与游戏规则保持一致 */
  .result-sanhua {
    color: #29B6F6 !important; /* 散花 - 中等蓝色 */
  }

  .result-duizi {
    color: #A5D6A7 !important; /* 对子 - 中等绿色 */
  }

  .result-shunzi {
    color: #FFCC02 !important; /* 顺子 - 金黄色 */
  }

  .result-baozi {
    color: #F8BBD9 !important; /* 豹子 - 浅粉色 */
  }

  .result-weight,
  .result-beer {
    font-size: 14rpx;
    color: #666;
    display: block;
    margin-bottom: 4rpx;
  }

  .result-profit {
    font-size: 16rpx;
    font-weight: bold;
    color: #27ae60;
    display: block;
  }

  /* 右：游戏记录面板 */
  .history-panel {
    background: rgba(255, 255, 255, 0.1);
    grid-area: history;
    padding: 10rpx;
    border-radius: 8rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: auto; /* 改为自动高度 */
    min-height: 300rpx; /* 设置最小高度 */
    max-height: 400rpx; /* 限制最大高度，与游戏区域一致 */
  }

  .history-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 320rpx; /* 固定最大高度，确保可滚动 */
    min-height: 200rpx; /* 最小高度 */
    padding-right: 5rpx; /* 为滚动条留出空间 */
  }

  /* 平板端滚动条样式 - 历史面板 */
  .history-content::-webkit-scrollbar {
    width: 4rpx;
  }

  .history-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2rpx;
  }

  .history-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2rpx;
  }

  .history-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  .no-history {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12rpx;
    padding: 15rpx 5rpx;
  }

  .history-list {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
    padding-right: 5rpx; /* 为滚动条留出空间 */
  }

  .history-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 3rpx 6rpx;
    border-radius: 3rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rpx;
  }

  .history-dice-container {
    display: flex;
    gap: 6rpx; /* 增加间隔，从2rpx改为6rpx */
    align-items: center;
  }

  .history-dice-single {
    width: 12rpx;
    height: 12rpx;
    background: white;
    border-radius: 2rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
    border: 0.5rpx solid rgba(0, 0, 0, 0.1);
  }

  .history-dice-number {
    font-size: 7rpx;
    font-weight: bold;
    color: #333;
    line-height: 1;
  }

  .history-type {
    font-size: 8rpx;
    color: white;
    font-weight: bold;
    line-height: 1.2;
  }

  /* 平板端游戏记录背景颜色 - 与新颜色方案协调 */
  .history-sanhua {
    background: linear-gradient(135deg, #29B6F6 0%, #1976D2 100%) !important;
    /* 散花 - 蓝色渐变背景 */
  }


  .history-sanhua .history-type,
  .history-sanhua  {
    color: white !important;
    /* 白色字体在紫色背景上 */
  }

  .history-duizi {
    background: linear-gradient(135deg, #A5D6A7 0%, #66BB6A 100%) !important;
    /* 对子 - 绿色渐变背景 */
  }

  .history-duizi .history-dice,
  .history-duizi .history-type,
  .history-duizi  {
    color: white !important;
    /* 白色字体在绿色背景上 */
  }

  .history-shunzi {
    background: linear-gradient(135deg, #FFCC02 0%, #FFA000 100%) !important;
    /* 顺子 - 金色渐变背景 */
  }

  .history-shunzi .history-dice,
  .history-shunzi .history-type,
  .history-shunzi  {
    color: white !important;
    /* 白色字体在金色背景上 */
  }

  .history-baozi {
    background: linear-gradient(135deg, #F8BBD9 0%, #EC407A 100%) !important;
    /* 豹子 - 粉色渐变背景 */
  }

  .history-baozi .history-dice,
  .history-baozi .history-type,
  .history-baozi  {
    color: white !important;
    /* 白色字体在粉色背景上 */
  }

  /* 下部：啤酒选择按钮 */
  .bottom-area {
    background: rgba(255, 255, 255, 0.1);
    grid-area: bottom;
    position: relative;
    padding: 10rpx;
    border-radius: 10rpx;
    box-sizing: border-box;
  }

  .bottom-actions {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    display: flex;
    gap: 4rpx;
    z-index: 10;
  }

  .beer-buttons {
    display: grid;
    grid-template-columns: 6fr 6fr 1fr 6fr 6fr;
    gap: 0;
    width: 100%;
  }

  .beer-btn {
    padding: 0;
    border-radius: 15rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 18rpx;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60rpx;
    box-sizing: border-box;
    transition: all 0.2s ease;
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx;
    padding: 12rpx 15rpx;
    height: 100%;
  }

  .btn-icon {
    font-size: 20rpx;
  }

  .btn-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rpx;
    height: 50rpx;
  }

  .btn-title {
    font-size: 13rpx;
    font-weight: bold;
    color: white;
    line-height: 30rpx;
    margin-bottom: -1rpx;
  }

  .btn-price {
    font-size: 11rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: normal;
    line-height: 20 rpx;
  }

  .beer-buttons .draft-btn {
    grid-column: 2;
  }

  .beer-buttons .fruit-btn {
    grid-column: 4;
  }

  .beer-btn:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  .beer-btn.active {
    border-color: rgba(255, 255, 255, 0.8);
  }

  .draft-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }

  .draft-btn.active .btn-title {
    color: white;
  }

  .draft-btn.active .btn-price {
    color: rgba(255, 255, 255, 0.9);
  }

  .fruit-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }

  .fruit-btn.active .btn-title {
    color: white;
  }

  .fruit-btn.active .btn-price {
    color: rgba(255, 255, 255, 0.9);
  }

  /* 平板端按钮禁用状态 */
  .beer-btn.disabled,
  .beer-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
  }
}

/* ================================= PC端样式 (1024px+) ================================= */
@media screen and (min-width: 1024px) {
  .game-container {
    min-height: 100vh;
    display: grid;
    grid-template-rows: auto 1fr auto;
    grid-template-areas:
      "header"
      "middle"
      "bottom";
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40rpx;
    gap: 40rpx;
    max-width: 1400rpx;
    margin: 0 auto;
  }

  /* 上部：标题栏 */
  .header-area {
    background: rgba(255, 255, 255, 0.1);
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 50rpx 60rpx;
    border-radius: 25rpx;
  }

  .rules-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 32rpx 48rpx;
    border-radius: 40rpx;
    font-size: 32rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    f

  .rules-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3rpx);
  }

  .header-buttons {
    display: flex;
    gap: 20rpx;
    flex-shrink: 0;
  }

  .header-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    padding: 25rpx 40rpx;
    border-radius: 30rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
  }

  .header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3rpx);
  }

  .title-section {
    text-align: center;
    flex: 1;
  }

  .main-title {
    font-size: 64rpx;
    font-weight: bold;
    color: white;
    display: block;
    margin-bottom: 16rpx;
  }

  .sub-title {
    font-size: 36rpx;
    color: rgba(255, 255, 255, 0.8);
    display: block;
  }

  /* 中部：三栏布局 */
  .middle-area {
    background: rgba(255, 255, 255, 0.05);
    grid-area: middle;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-areas: "rules game history";
    gap: 35rpx;
    padding: 40rpx;
    border-radius: 25rpx;
    transition: grid-template-columns 0.3s ease;
    align-items: stretch;
  }

  /* 隐藏规则面板时的布局 */
  .middle-area.hide-rules {
    grid-template-columns: 0 3fr 1fr;
    grid-template-areas: "game game history";
  }

  /* 隐藏历史面板时的布局 */
  .middle-area.hide-history {
    grid-template-columns: 1fr 3fr 0;
    grid-template-areas: "rules game game";
  }

  /* 隐藏两个面板时的布局 */
  .middle-area.hide-both {
    grid-template-columns: 0 1fr 0;
    grid-template-areas: "game game game";
  }

  /* 左：游戏规则面板 */
  .rules-panel {
    
    grid-area: rules;
    padding: 40rpx;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-height: 100%;
  }

  .panel-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 25rpx;
  }

  .panel-title {
    font-size: 36rpx;
    font-weight: bold;
    color: white;
    display: block;
  }

  .record-count {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.7);
    display: block;
  }

  .action-btn {
    padding: 8rpx 16rpx;
    border-radius: 15rpx;
    font-size: 20rpx;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
  }

  .clear-btn {
    background: rgba(255, 107, 107, 0.8);
    color: white;
  }

  .clear-btn:hover {
    background: rgba(255, 107, 107, 1);
    transform: translateY(-2rpx);
  }

  .analysis-btn {
    background: rgba(103, 126, 234, 0.8);
    color: white;
  }

  .analysis-btn:hover {
    background: rgba(103, 126, 234, 1);
    transform: translateY(-2rpx);
  }

  .rules-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    flex: 1;
  }

  .rule-section {
    margin-bottom: 20rpx;
  }

  .section-subtitle {
    font-size: 24rpx;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.95);
    display: block;
    margin-bottom: 12rpx;
    text-align: center;
  }

  .rule-list {
    display: flex;
    flex-direction: column;
    gap: 10rpx;
  }

  .rule-item-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 8rpx;
    padding: 12rpx 10rpx;
    margin-bottom: 6rpx;
  }

  .rule-item {
    font-size: 22rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 4rpx;
    line-height: 1.3;
  }

  .rule-desc {
    font-size: 18rpx;
    color: rgba(255, 255, 255, 0.7);
    display: block;
    margin-bottom: 4rpx;
    text-align: center;
    line-height: 1.3;
  }

  .rule-reward {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    display: block;
    text-align: center;
    line-height: 1.3;
  }

  .rule-extra {
    font-size: 18rpx;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    text-align: center;
    margin-bottom: 8rpx;
    background: rgba(255, 255, 255, 0.05);
    padding: 8rpx;
    border-radius: 6rpx;
    line-height: 1.4;
  }

  /* PC端游戏规则字体颜色 - 大屏幕优化 */
  .rule-sanhua {
    color: #81D4FA !important;
    /* 散花 - 浅蓝色，与豹子粉色形成明显区分 */
  }

  .rule-duizi {
    color: #C8E6C9 !important;
    /* 对子 - 浅绿色，清新自然 */
  }

  .rule-shunzi {
    color: #FFE082 !important;
    /* 顺子 - 浅金色，温暖明亮 */
  }

  .rule-baozi {
    color: #F8BBD9 !important;
    /* 豹子 - 浅粉色，优雅突出 */
  }

  /* 中：游戏区域 */
  .game-area {
    
    grid-area: game;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    min-height: 100%;
  }

  .dice-container {
    display: flex;
    gap: 40rpx;
    margin-bottom: 50rpx;
    align-items: center;
    justify-content: center;
  }

  /* PC端确保骰子之间有间距 */
  .dice-container > * {
    margin: 0 20rpx;
  }

  .dice-container > *:first-child {
    margin-left: 0;
  }

  .dice-container > *:last-child {
    margin-right: 0;
  }

  .game-result {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 30rpx;
    border-radius: 20rpx;
    width: 80%; /* 调整为80%宽度 */
    max-width: 600rpx; /* 限制最大宽度 */
    margin: 30rpx auto 0; /* 居中显示 */
    min-height: 100rpx; /* PC端固定最小高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15); /* 添加阴影效果 */
  }

  .result-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60rpx; /* PC端内容区域固定高度 */
  }

  .result-prompt {
    font-size: 36rpx;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    line-height: 1.2;
  }

  .result-rolling {
    font-size: 36rpx;
    font-weight: bold;
    color: #ffa500;
    display: block;
    line-height: 1.2;
    animation: rolling-pulse 1s ease-in-out infinite;
  }

  .dice-numbers {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    background: rgba(0, 0, 0, 0.05);
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
  }

  .result-type {
    font-size: 18rpx;
    font-weight: bold;
    color: #ff6b6b;
    display: block;
    margin-bottom: 2rpx;
  }

  /* PC端游戏结果类型颜色 - 与游戏规则保持一致 */
  .result-sanhua {
    color: #81D4FA !important; /* 散花 - 浅蓝色 */
  }

  .result-duizi {
    color: #C8E6C9 !important; /* 对子 - 浅绿色 */
  }

  .result-shunzi {
    color: #FFE082 !important; /* 顺子 - 浅金色 */
  }

  .result-baozi {
    color: #F8BBD9 !important; /* 豹子 - 浅粉色 */
  }

  .result-weight,
  .result-beer {
    font-size: 32rpx;
    color: #666;
    display: block;
    margin-bottom: 12rpx;
  }

  .result-profit {
    font-size: 36rpx;
    font-weight: bold;
    color: #27ae60;
    display: block;
  }

  /* 右：游戏记录面板 */
  .history-panel {
  
    grid-area: history;
    padding: 40rpx;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-height: 100%;
  }

  .history-content {
    flex: 1;
    overflow-y: auto;
  }

  .no-history {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 30rpx;
    padding: 40rpx;
  }

  .history-list {
    display: flex;
    flex-direction: column;
    gap: 15rpx;
  }

  .history-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 12rpx 16rpx;
    border-radius: 8rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .history-dice-container {
    display: flex;
    gap: 6rpx;
    align-items: center;
  }

  .history-dice-single {
    width: 32rpx;
    height: 32rpx;
    background: white;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.2);
    border: 1rpx solid rgba(0, 0, 0, 0.1);
  }

  .history-dice-number {
    font-size: 18rpx;
    font-weight: bold;
    color: #333;
    line-height: 1;
  }

  .history-type {
    font-size: 24rpx;
    color: white;
    font-weight: bold;
  }

  /* PC端游戏记录背景颜色 - 与新颜色方案协调 */
  .history-sanhua {
    background: linear-gradient(135deg, #81D4FA 0%, #42A5F5 100%) !important;
    /* 散花 - 浅蓝色渐变背景 */
  }


  .history-sanhua .history-type,
  .history-sanhua  {
    color: white !important;
    /* 白色字体在紫色背景上 */
  }

  .history-duizi {
    background: linear-gradient(135deg, #C8E6C9 0%, #81C784 100%) !important;
    /* 对子 - 浅绿色渐变背景 */
  }

  .history-duizi .history-dice,
  .history-duizi .history-type,
  .history-duizi  {
    color: white !important;
    /* 白色字体在绿色背景上 */
  }

  .history-shunzi {
    background: linear-gradient(135deg, #FFE082 0%, #FFB300 100%) !important;
    /* 顺子 - 浅金色渐变背景 */
  }

  .history-shunzi .history-dice,
  .history-shunzi .history-type,
  .history-shunzi  {
    color: white !important;
    /* 白色字体在金色背景上 */
  }

  .history-baozi {
    background: linear-gradient(135deg, #F8BBD9 0%, #F06292 100%) !important;
    /* 豹子 - 浅粉色渐变背景 */
  }

  .history-baozi .history-dice,
  .history-baozi .history-type,
  .history-baozi  {
    color: white !important;
    /* 白色字体在粉色背景上 */
  }

  /* 下部：啤酒选择按钮 */
  .bottom-area {
    background: rgba(255, 255, 255, 0.1);
    grid-area: bottom;
    position: relative;
    padding: 45rpx;
    border-radius: 25rpx;
  }

  .bottom-actions {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    display: flex;
    gap: 12rpx;
    z-index: 10;
  }

  .beer-buttons {
    display: grid;
    grid-template-columns: 1fr 3fr 2fr 3fr 1fr;
    gap: 0;
    width: 100%;
  }

  .beer-btn {
    padding: 0;
    border-radius: 25rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 36rpx;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15rpx;
    padding: 45rpx 35rpx;
    height: 100%;
  }

  .btn-icon {
    font-size: 48rpx;
  }

  .btn-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0rpx;
    line-height: 1;
  }

  .btn-title {
    font-size: 28rpx;
    font-weight: bold;
    color: white;
    line-height: 1;
    margin-bottom: -2rpx;
  }

  .btn-price {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: normal;
    line-height: 1;
  }

  .beer-buttons .draft-btn {
    grid-column: 2;
  }

  .beer-buttons .fruit-btn {
    grid-column: 4;
  }

  .beer-btn:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  .beer-btn.active {
    border-color: rgba(255, 255, 255, 0.8);
  }

  .draft-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }

  .draft-btn.active .btn-title {
    color: white;
  }

  .draft-btn.active .btn-price {
    color: rgba(255, 255, 255, 0.9);
  }

  .fruit-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }

  .fruit-btn.active .btn-title {
    color: white;
  }

  .fruit-btn.active .btn-price {
    color: rgba(255, 255, 255, 0.9);
  }

  /* PC端按钮禁用状态 */
  .beer-btn.disabled,
  .beer-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
  }
}}

/* 中大奖动画样式 */
.jackpot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: jackpotFadeIn 0.5s ease-out;
}

.jackpot-container {
  position: relative;
  text-align: center;
  animation: jackpotBounce 0.8s ease-out;
}

.jackpot-content {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  padding: 60rpx 40rpx;
  border-radius: 30rpx;
  box-shadow: 0 20rpx 60rpx rgba(255, 215, 0, 0.5);
  border: 6rpx solid #FFD700;
  position: relative;
  overflow: hidden;
}

.jackpot-content::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: jackpotShine 2s infinite;
}

.jackpot-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
  animation: jackpotPulse 1s infinite alternate;
}

.jackpot-subtitle {
  font-size: 32rpx;
  color: #8B4513;
  display: block;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.jackpot-dice {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.jackpot-dice-item {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  animation: jackpotDiceRotate 1s infinite linear;
}

.jackpot-dice-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.jackpot-reward {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 烟花效果 */
.jackpot-fireworks {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.firework {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  animation: fireworkExplode 2s infinite;
}

.firework-1 {
  top: 20%;
  left: 20%;
  background: #FF6B6B;
  animation-delay: 0s;
}

.firework-2 {
  top: 30%;
  right: 20%;
  background: #4ECDC4;
  animation-delay: 0.5s;
}

.firework-3 {
  bottom: 30%;
  left: 30%;
  background: #45B7D1;
  animation-delay: 1s;
}

.firework-4 {
  bottom: 20%;
  right: 30%;
  background: #96CEB4;
  animation-delay: 1.5s;
}

/* 闪烁星星效果 */
.jackpot-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 30rpx;
  animation: sparkleFloat 3s infinite ease-in-out;
}

.sparkle-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 15%;
  right: 15%;
  animation-delay: 0.5s;
}

.sparkle-3 {
  top: 50%;
  left: 5%;
  animation-delay: 1s;
}

.sparkle-4 {
  top: 50%;
  right: 5%;
  animation-delay: 1.5s;
}

.sparkle-5 {
  bottom: 15%;
  left: 15%;
  animation-delay: 2s;
}

.sparkle-6 {
  bottom: 10%;
  right: 10%;
  animation-delay: 2.5s;
}

/* 动画关键帧 */
@keyframes jackpotFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes jackpotBounce {
  0% {
    transform: scale(0.3) translateY(-100rpx);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes jackpotPulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes jackpotShine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes jackpotDiceRotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

@keyframes fireworkExplode {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(3);
    opacity: 0.8;
  }
  100% {
    transform: scale(6);
    opacity: 0;
  }
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 1;
  }
}