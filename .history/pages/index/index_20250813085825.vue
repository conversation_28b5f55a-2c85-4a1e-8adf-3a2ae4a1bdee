<!--
 * @Description: 骰子游戏主页面 - Grid布局
-->
<template>
  <view class="game-container">
    <!-- 上部：标题栏 -->
    <view class="header-area">
      <button class="rules-btn" @click="handleRulesClick">📋 游戏规则</button>
      <view class="title-section">
        <text class="main-title">🎲 骰子啤酒游戏</text>
        <text class="sub-title">摇出点数，畅饮啤酒</text>
      </view>
      <view class="header-buttons">
        <button class="header-btn" @click="handleHistoryClick">游戏记录</button>
      </view>
    </view>

    <!-- 中部：三栏布局 -->
    <view class="middle-area" :class="middleAreaClass">
      <!-- 左：游戏规则面板 -->
      <view
        v-show="showRulesPanel"
        class="rules-panel animate__animated"
        :class="rulesAnimationClass"
      >
        <text class="panel-title">🎲 游戏规则</text>
        <view class="rules-content">
          <view class="rule-section">
            <view class="rule-list">
              <view class="rule-item-wrapper">
                <text class="rule-item rule-sanhua">散花</text>
                <text class="rule-desc rule-sanhua">三个不同点数</text>
                <text class="rule-reward rule-sanhua">1斤啤酒</text>
              </view>
              <view class="rule-item-wrapper">
                <text class="rule-item rule-duizi">对子</text>
                <text class="rule-desc rule-duizi">两个相同点数</text>
                <text class="rule-reward rule-duizi">2斤啤酒</text>
              </view>
              <view class="rule-item-wrapper">
                <text class="rule-item rule-shunzi">顺子</text>
                <text class="rule-desc rule-shunzi">连续三个点数</text>
                <text class="rule-reward rule-shunzi">3斤啤酒</text>
              </view>
              <view class="rule-item-wrapper">
                <text class="rule-item rule-baozi">豹子</text>
                <text class="rule-desc rule-baozi">三个相同点数</text>
                <text class="rule-reward rule-baozi">4斤啤酒</text>
              </view>
            </view>
          </view>
          <view class="rule-section">
            <text class="section-subtitle">💰 特殊优惠</text>
            <text class="rule-extra rule-baozi">加10元可换3升原浆</text>
            <text class="rule-extra rule-baozi">架子可退或换1斤原浆</text>
          </view>
        </view>
      </view>

      <!-- 中：游戏区域 -->
      <view class="game-area">
        <view class="dice-container">
          <Dice
            v-for="(dice, index) in diceValues"
            :key="index"
            :ref="(el) => (diceRefs[index] = el)"
            :value="dice"
            :size="getDiceSize()"
            :animated="true"
          />
        </view>

        <view class="game-result">
          <view class="result-content">
            <text v-if="isRolling" class="result-rolling">🎲 摇骰子中...</text>
            <text v-else-if="!gameResult" class="result-prompt"
              >🎰 摇出大奖，畅饮啤酒！</text
            >
            <template v-else>
              <view class="dice-display">
                <text class="dice-numbers" :class="getResultTypeClass(gameResult.diceType)">
                  {{ gameResult.dice.join(" - ") }}
                  <text class="result-type"  >
                    {{ gameResult.diceType }} {{ gameResult.weight }} 斤
                    {{ gameResult.beerType === "draft" ? "原浆" : "果啤" }}
                  </text>
                </text>
              </view>
            </template>
          </view>
        </view>
      </view>

      <!-- 右：游戏记录面板 -->
      <view
        v-show="showHistoryPanel"
        class="history-panel animate__animated"
        :class="historyAnimationClass"
      >
        <view class="panel-header">
          <text class="panel-title">📊 游戏记录</text>
        </view>
        <view class="history-content">
          <view v-if="allHistory.length === 0" class="no-history">
            <text>暂无记录</text>
          </view>
          <view v-else class="history-list">
            <view
              v-for="(record, index) in recentHistory"
              :key="index"
              class="history-item"
              :class="getHistoryItemClass(record.点数类型 || record.diceType)"
            >
              <view class="history-dice-container">
                <view
                  v-for="(dice, diceIndex) in record.骰子点数 || record.dice"
                  :key="diceIndex"
                  class="history-dice-single"
                >
                  <text class="history-dice-number">{{ dice }}</text>
                </view>
              </view>
              <text class="history-type">{{
                record.点数类型 || record.diceType
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 中大奖动画 -->
    <view v-if="showJackpotAnimation" class="jackpot-overlay">
      <view class="jackpot-container">
        <view class="jackpot-fireworks">
          <view class="firework firework-1"></view>
          <view class="firework firework-2"></view>
          <view class="firework firework-3"></view>
          <view class="firework firework-4"></view>
        </view>
        <view class="jackpot-content">
          <text class="jackpot-title">🎉 中大奖啦！🎉</text>
          <text class="jackpot-subtitle">恭喜获得豹子！</text>
          <view class="jackpot-dice">
            <view
              v-for="(dice, index) in jackpotDice"
              :key="index"
              class="jackpot-dice-item"
            >
              <text class="jackpot-dice-number">{{ dice }}</text>
            </view>
          </view>
          <text class="jackpot-reward">奖励：4斤啤酒</text>
        </view>
        <view class="jackpot-sparkles">
          <view class="sparkle sparkle-1">✨</view>
          <view class="sparkle sparkle-2">⭐</view>
          <view class="sparkle sparkle-3">💫</view>
          <view class="sparkle sparkle-4">🌟</view>
          <view class="sparkle sparkle-5">✨</view>
          <view class="sparkle sparkle-6">⭐</view>
        </view>
      </view>
    </view>

    <!-- 下部：啤酒选择按钮 -->
    <view class="bottom-area">
      <!-- 右上角按钮组 -->
      <view class="bottom-actions">
        <button class="action-btn clear-btn" @click="clearHistory">
          清除记录
        </button>
        <button
          class="action-btn analysis-btn"
          @click="showAnalysisModal = true"
        >
          记录分析
        </button>
      </view>

      <!-- 啤酒选择按钮 -->
      <view class="beer-buttons">
        <button
          class="beer-btn draft-btn"
          :class="{
            active: selectedBeer === BeverageType.YUAN_JIANG,
            disabled: buttonsDisabled || isRolling,
          }"
          :disabled="buttonsDisabled || isRolling"
          @click="selectBeer(BeverageType.YUAN_JIANG)"
        >
          <view class="btn-content">
            <text class="btn-icon">🍺</text>
            <view class="btn-text">
              <text class="btn-title">原浆啤酒</text>
              <text class="btn-price">¥ 10 元/次</text>
            </view>
          </view>
        </button>
        <button
          class="beer-btn fruit-btn"
          :class="{
            active: selectedBeer === BeverageType.GUO_PI,
            disabled: buttonsDisabled || isRolling,
          }"
          :disabled="buttonsDisabled || isRolling"
          @click="selectBeer(BeverageType.GUO_PI)"
        >
          <view class="btn-content">
            <text class="btn-icon">🍹</text>
            <view class="btn-text">
              <text class="btn-title">果味啤酒</text>
              <text class="btn-price">¥ 15 元/次</text>
            </view>
          </view>
        </button>
      </view>
    </view>

    <!-- 规则弹窗 -->
    <RulesModal :visible="showRulesModal" @close="closeRulesModal" />

    <!-- 记录分析弹窗 -->
    <AnalysisModal
      :visible="showAnalysisModal"
      :history="allHistory"
      @close="showAnalysisModal = false"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import diceCalculator, { BeverageType } from "../../utils/diceCalculator.js";
import RulesModal from "../../components/RulesModal.vue";
import AnalysisModal from "../../components/AnalysisModal.vue";
import Dice from "../../components/dice.vue";

// 响应式数据
const diceValues = ref([1, 2, 3]);
const gameResult = ref(null);
const isRolling = ref(false); // 添加摇骰子状态
const selectedBeer = ref(BeverageType.YUAN_JIANG);
const showRulesModal = ref(false);
const showAnalysisModal = ref(false);
const allHistory = ref([]);
const diceRefs = ref([]); // 骰子组件引用数组
const buttonsDisabled = ref(false); // 按钮禁用状态
let restartAnimationTimer = null; // 重启动画定时器
const showJackpotAnimation = ref(false); // 中大奖动画显示状态
const jackpotDice = ref([]); // 中大奖时的骰子数值
let jackpotAnimationTimer = null; // 中大奖动画定时器

// 面板显示控制
const showRulesPanel = ref(true);
const showHistoryPanel = ref(true);
const rulesAnimationClass = ref("");
const historyAnimationClass = ref("");
const deviceType = ref("");

// 计算历史记录显示（显示所有记录，最新的在前）
const recentHistory = computed(() => {
  return [...allHistory.value].reverse();
});

// 计算中部区域的类名
const middleAreaClass = computed(() => {
  const classes = [];
  if (!showRulesPanel.value) classes.push("hide-rules");
  if (!showHistoryPanel.value) classes.push("hide-history");
  if (!showRulesPanel.value && !showHistoryPanel.value)
    classes.push("hide-both");
  return classes.join(" ");
});

// 页面加载
onLoad(() => {
  let info = uni.getSystemInfoSync();
  console.log("app is onload... ", info.deviceType);
  deviceType.value = info.deviceType;

  // 移动端默认隐藏面板
  if (info.deviceType === "phone") {
    showRulesPanel.value = false;
    showHistoryPanel.value = false;
  }

  loadHistory();
});

// 页面显示时重新激活骰子动画
const onShow = () => {
  if (!gameResult.value && !isRolling.value) {
    // 只有在显示默认状态时才重新激活动画
    setTimeout(() => {
      restartDiceAnimations();
    }, 500);
  }
};

// 监听页面显示
uni.onShow && uni.onShow(onShow);

// 加载历史记录
const loadHistory = () => {
  try {
    // 先尝试新键名
    let savedHistory = uni.getStorageSync("dice_records");
    if (savedHistory) {
      allHistory.value = JSON.parse(savedHistory);
      return;
    }

    // 兼容旧键名，迁移后删除
    savedHistory = uni.getStorageSync("diceGameHistory");
    if (savedHistory) {
      allHistory.value = JSON.parse(savedHistory);
      // 迁移到新键名
      uni.setStorageSync("dice_records", savedHistory);
      uni.removeStorageSync("diceGameHistory");
    } else {
      // 默认为空数组，不从 diceCalculator 加载
      allHistory.value = [];
    }
  } catch (e) {
    console.error("加载历史记录失败:", e);
    allHistory.value = [];
  }
};

// 保存游戏结果到本地存储
const saveGameResult = (result) => {
  try {
    // 创建历史记录项
    const historyItem = {
      dice: result.dice,
      diceType: result.diceType,
      weight: result.weight,
      beerType: result.beerType,
      timestamp: Date.now(),
      date: new Date().toLocaleString(),
    };

    // 添加到历史记录数组（不限制数量）
    allHistory.value.push(historyItem);

    // 保存到本地存储 - 使用新的键名
    uni.setStorageSync("dice_records", JSON.stringify(allHistory.value));

    // 同时保存到 diceCalculator（保持兼容性）
    diceCalculator.saveHistory(historyItem);
  } catch (e) {
    console.error("保存游戏结果失败:", e);
  }
};

// 清除所有历史记录
const clearHistory = () => {
  try {
    // 清空历史记录数组
    allHistory.value = [];
    // 清除 diceCalculator 中的记录（保持兼容性）
    diceCalculator.clearHistory();

    // 清除可能正在显示的动画和定时器
    if (showJackpotAnimation.value) {
      showJackpotAnimation.value = false;
      jackpotDice.value = [];
    }
    if (jackpotAnimationTimer) {
      clearTimeout(jackpotAnimationTimer);
      jackpotAnimationTimer = null;
    }
    if (restartAnimationTimer) {
      clearTimeout(restartAnimationTimer);
      restartAnimationTimer = null;
    }

    // 重置状态
    gameResult.value = null;
    isRolling.value = false;
    buttonsDisabled.value = false;

    uni.showToast({
      title: "记录已清除",
      icon: "success",
    });
  } catch (e) {
    console.error("清除记录失败:", e);
    uni.showToast({
      title: "清除失败",
      icon: "error",
    });
  }
};

// 重新激活所有骰子的连续旋转动画
const restartDiceAnimations = () => {
  // 重置骰子数值到初始状态
  diceValues.value = [1, 2, 3];

  // 等待DOM更新后重新启动动画
  nextTick(() => {
    diceRefs.value.forEach((diceRef) => {
      if (diceRef && diceRef.restartContinuousAnimation) {
        diceRef.restartContinuousAnimation();
      }
    });
  });
};

// 选择啤酒类型并摇骰子
const selectBeer = (type) => {
  // 如果按钮被禁用或正在摇骰子，忽略点击
  if (buttonsDisabled.value || isRolling.value) {
    return;
  }

  selectedBeer.value = type;
  rollDice();
};

// 摇骰子
const rollDice = () => {
  // 如果正在摇骰子，忽略新的点击
  if (isRolling.value) {
    return;
  }

  // 清除之前的重启动画定时器
  if (restartAnimationTimer) {
    clearTimeout(restartAnimationTimer);
    restartAnimationTimer = null;
  }

  const result = diceCalculator.calculateGameResult(selectedBeer.value);

  // 清空之前的结果，设置摇骰子状态，禁用按钮
  gameResult.value = null;
  isRolling.value = true;
  buttonsDisabled.value = true;

  // 第一阶段：快速随机变化骰子数值（1秒）
  let animationCount = 0;
  const quickAnimations = 12; // 减少变化次数，更快结束
  let currentSpeed = 50; // 更快的初始速度

  const quickRoll = () => {
    // 使用随机数值进行快速动画，但不影响最终结果
    const randomDice = diceCalculator.generateDice();
    diceValues.value = randomDice;
    animationCount++;

    if (animationCount >= quickAnimations) {
      // 第一阶段结束，开始第二阶段
      startFinalAnimation();
    } else {
      // 继续快速变化，速度逐渐加快
      currentSpeed = Math.max(currentSpeed - 3, 25); // 速度从50ms逐渐减少到25ms
      setTimeout(quickRoll, currentSpeed);
    }
  };

  // 第二阶段：设置最终结果并触发骰子动画
  const startFinalAnimation = () => {
    // 设置最终的骰子数值，这会触发骰子组件的平滑过渡动画
    diceValues.value = [...result.dice]; // 使用扩展运算符确保数组完全更新

    // 等待骰子动画完成后显示结果（骰子动画1.2秒 + 0.3秒缓冲）
    setTimeout(() => {
      // 确保所有骰子停止动画并显示正确的面
      diceRefs.value.forEach((diceRef, index) => {
        if (diceRef && diceRef.stopAnimation) {
          diceRef.stopAnimation();
        }
      });

      // 再次确保骰子数值正确（防止异步问题）
      diceValues.value = [...result.dice];

      // 短暂延迟后显示结果，确保骰子完全停止在正确位置
      setTimeout(() => {
        isRolling.value = false; // 结束摇骰子状态
        gameResult.value = result;

        // 保存到历史记录和本地存储
        saveGameResult(result);
        loadHistory(); // 更新历史记录显示

        // 如果是豹子，显示中大奖动画
        if (result.diceType === "豹子") {
          showJackpotAnimation.value = true;
          jackpotDice.value = [...result.dice];

          // 3秒后关闭动画
          jackpotAnimationTimer = setTimeout(() => {
            showJackpotAnimation.value = false;
            jackpotDice.value = [];
          }, 3000);
        }

        // 2秒后切换回游戏大奖提示并重新启动动画
        restartAnimationTimer = setTimeout(() => {
          gameResult.value = null;
          buttonsDisabled.value = false; // 重新启用按钮
          // 重新激活所有骰子的连续旋转动画
          restartDiceAnimations();
        }, 2000); // 2秒后恢复，符合任务要求
      }, 200); // 额外200ms确保骰子完全停止
    }, 1500); // 1.5秒后显示结果，骰子动画完成
  };

  // 开始动画
  quickRoll();
};

// 切换游戏规则面板
const toggleRulesPanel = () => {
  if (showRulesPanel.value) {
    // 隐藏面板
    rulesAnimationClass.value = "animate__slideOutLeft";
    setTimeout(() => {
      showRulesPanel.value = false;
      rulesAnimationClass.value = "";
    }, 500);
  } else {
    // 显示面板
    showRulesPanel.value = true;
    rulesAnimationClass.value = "animate__slideInLeft";
    setTimeout(() => {
      rulesAnimationClass.value = "";
    }, 500);
  }
};

// 切换游戏记录面板
const toggleHistoryPanel = () => {
  if (showHistoryPanel.value) {
    // 隐藏面板
    historyAnimationClass.value = "animate__slideOutRight";
    setTimeout(() => {
      showHistoryPanel.value = false;
      historyAnimationClass.value = "";
    }, 500);
  } else {
    // 显示面板
    showHistoryPanel.value = true;
    historyAnimationClass.value = "animate__slideInRight";
    setTimeout(() => {
      historyAnimationClass.value = "";
    }, 500);
  }
};

// 显示游戏规则弹窗（移动端使用）
const showRules = () => {
  showRulesModal.value = true;
};

// 关闭游戏规则弹窗
const closeRulesModal = () => {
  showRulesModal.value = false;
};

// 显示游戏记录页面（移动端使用）
const showHistory = () => {
  uni.navigateTo({
    url: "/pages/history/history",
  });
};

// 处理规则按钮点击
const handleRulesClick = () => {
  if (deviceType.value === "phone") {
    // 移动端使用弹窗
    showRules();
  } else {
    // 平板和PC端使用面板切换
    toggleRulesPanel();
  }
};

// 处理历史按钮点击
const handleHistoryClick = () => {
  if (deviceType.value === "phone") {
    // 移动端跳转到历史页面
    showHistory();
  } else {
    // 平板和PC端使用面板切换
    toggleHistoryPanel();
  }
};

// 获取历史记录项的样式类名
const getHistoryItemClass = (diceType) => {
  switch (diceType) {
    case "散花":
      return "history-sanhua";
    case "对子":
      return "history-duizi";
    case "顺子":
      return "history-shunzi";
    case "豹子":
      return "history-baozi";
    default:
      return "";
  }
};

// 获取游戏结果类型的样式类名
const getResultTypeClass = (diceType) => {
  switch (diceType) {
    case "散花":
      return "result-sanhua";
    case "对子":
      return "result-duizi";
    case "顺子":
      return "result-shunzi";
    case "豹子":
      return "result-baozi";
    default:
      return "";
  }
};

// 根据设备类型获取骰子尺寸
const getDiceSize = () => {
  if (deviceType.value === "phone") {
    return 50; // 移动端 - 增大尺寸确保可见
  } else {
    // 使用 uni.getSystemInfoSync() 获取屏幕信息
    try {
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth =
        systemInfo.screenWidth || systemInfo.windowWidth || 768;

      if (screenWidth >= 1024) {
        return 60; // PC端 - 增大尺寸
      } else {
        return 50; // 平板端 - 增大尺寸
      }
    } catch (e) {
      // 如果获取系统信息失败，根据设备类型返回默认值
      return deviceType.value === "pc" ? 100 : 80;
    }
  }
};

onMounted(() => {
  // 初始化显示
  diceValues.value = [1, 2, 3];
  gameResult.value = null; // 确保初始状态显示游戏大奖提示

  // 确保骰子动画正常启动
  nextTick(() => {
    restartDiceAnimations();
  });
});
</script>

<style src="./index.css" scoped></style>