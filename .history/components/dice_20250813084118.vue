<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";

// requestAnimationFrame 兼容性处理
const requestAnimFrame = (() => {
  return (
    (typeof window !== "undefined" && window.requestAnimationFrame) ||
    (typeof global !== "undefined" && global.requestAnimationFrame) ||
    ((callback) => setTimeout(callback, 16)) // 60fps fallback
  );
})();

const cancelAnimFrame = (() => {
  return (
    (typeof window !== "undefined" && window.cancelAnimationFrame) ||
    (typeof global !== "undefined" && global.cancelAnimationFrame) ||
    clearTimeout
  );
})();

// 组件属性
const props = defineProps({
  value: {
    type: Number,
    default: 1,
    validator: (value) => value >= 1 && value <= 6,
  },
  size: {
    type: Number,
    default: 80, // 默认尺寸 80rpx
  },
  animated: {
    type: Boolean,
    default: false,
  },
});

// 立方体尺寸
const cubeSize = computed(() => props.size);

// 立方体的整体变换
const rotationX = ref(0);
const rotationY = ref(0);
const rotationZ = ref(0);

// 动画控制
const isAnimating = ref(false);
let animationId = ref(null);

// 6个面的配置 - 按真实骰子标准排列（相对面相加等于7）
const faces = [
  { dots: 1, color: "#d32f2f" }, // 1点红色
  { dots: 6, color: "#1565c0" }, // 6点蓝色（1的对面）
  { dots: 3, color: "#1565c0" }, // 3点蓝色
  { dots: 4, color: "#d32f2f" }, // 4点红色（3的对面）
  { dots: 2, color: "#1565c0" }, // 2点蓝色
  { dots: 5, color: "#1565c0" }, // 5点蓝色（2的对面）
];

// 计算立方体的半边长（用于3D定位）
const halfCubeSize = computed(() => cubeSize.value / 2);

// 计算点的内边距（相对于面的大小）
const dotPadding = computed(() => Math.max(cubeSize.value * 0.25, 15));

// 计算点的大小（相对于面的大小）
const dotSize = computed(() => Math.max(cubeSize.value * 0.12, 8));

// 立方体场景的样式
const cubeSceneStyle = computed(() => ({
  width: `${cubeSize.value}rpx`,
  height: `${cubeSize.value}rpx`,
  transform: cubeTransform.value,
}));

// 面的样式
const faceStyle = computed(() => ({
  width: `${cubeSize.value}rpx`,
  height: `${cubeSize.value}rpx`,
  padding: `${dotPadding.value}rpx`,
}));

// 根据点数设置旋转角度
const setRotationForValue = (value) => {
  switch (value) {
    case 1: // 显示1点 - face-1 (前面)
      rotationX.value = 0;
      rotationY.value = 0;
      rotationZ.value = 0;
      break;
    case 2: // 显示2点 - face-5 (上面)
      rotationX.value = -90;
      rotationY.value = 0;
      rotationZ.value = 0;
      break;
    case 3: // 显示3点 - face-3 (右面)
      rotationX.value = 0;
      rotationY.value = -90;
      rotationZ.value = 0;
      break;
    case 4: // 显示4点 - face-4 (左面)
      rotationX.value = 0;
      rotationY.value = 90;
      rotationZ.value = 0;
      break;
    case 5: // 显示5点 - face-6 (下面)
      rotationX.value = 90;
      rotationY.value = 0;
      rotationZ.value = 0;
      break;
    case 6: // 显示6点 - face-2 (后面)
      rotationX.value = 0;
      rotationY.value = 180;
      rotationZ.value = 0;
      break;
    default:
      rotationX.value = 0;
      rotationY.value = 0;
      rotationZ.value = 0;
      break;
  }
};

// 旧的启动旋转动画函数已删除，现在使用 smoothTransitionToValue

// 停止旋转动画
const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimFrame(animationId.value);
    animationId.value = null;
  }
  isAnimating.value = false;
  // 确保骰子停在正确的面上
  setRotationForValue(props.value);
};

// 立方体变换
const cubeTransform = computed(
  () =>
    `rotateX(${rotationX.value}deg) rotateY(${rotationY.value}deg) rotateZ(${rotationZ.value}deg)`
);

// 监听 value 变化
const updateDice = () => {
  // 使用平滑过渡到指定点数
  smoothTransitionToValue(props.value);
};

// 角度标准化函数，将角度限制在 0-360 度范围内
const normalizeAngle = (angle) => {
  return ((angle % 360) + 360) % 360;
};

// 启动连续旋转动画（初始状态）
const startContinuousAnimation = () => {
  // 确保先停止任何现有动画
  stopAnimation();

  isAnimating.value = true;

  // 为每个骰子生成独特的旋转速度，增加随机性
  const baseSpeedX = 2.5 + Math.random() * 1; // 2.5-3.5
  const baseSpeedY = 3.5 + Math.random() * 1; // 3.5-4.5
  const baseSpeedZ = 1.5 + Math.random() * 1; // 1.5-2.5

  const animate = () => {
    if (isAnimating.value) {
      // 使用略微变化的速度，让旋转更自然
      rotationX.value += baseSpeedX + Math.sin(Date.now() * 0.001) * 0.5;
      rotationY.value += baseSpeedY + Math.cos(Date.now() * 0.0015) * 0.5;
      rotationZ.value += baseSpeedZ + Math.sin(Date.now() * 0.0008) * 0.3;

      // 平滑的角度管理：当角度过大时，减去完整的圈数
      // 这样可以保持视觉连续性，避免跳跃
      if (Math.abs(rotationX.value) > 3600) {
        // 每10圈管理一次
        const fullRotations =
          Math.floor(Math.abs(rotationX.value) / 3600) * 3600;
        rotationX.value -= Math.sign(rotationX.value) * fullRotations;
      }
      if (Math.abs(rotationY.value) > 3600) {
        const fullRotations =
          Math.floor(Math.abs(rotationY.value) / 3600) * 3600;
        rotationY.value -= Math.sign(rotationY.value) * fullRotations;
      }
      if (Math.abs(rotationZ.value) > 3600) {
        const fullRotations =
          Math.floor(Math.abs(rotationZ.value) / 3600) * 3600;
        rotationZ.value -= Math.sign(rotationZ.value) * fullRotations;
      }

      animationId.value = requestAnimFrame(animate);
    }
  };
  animate();
};

// 平滑过渡到指定点数
const smoothTransitionToValue = (targetValue) => {
  if (!props.animated) {
    setRotationForValue(targetValue);
    return;
  }

  // 停止当前动画
  stopAnimation();

  // 获取当前角度（不标准化，保持连续性）
  const startRotationX = rotationX.value;
  const startRotationY = rotationY.value;
  const startRotationZ = rotationZ.value;

  // 计算目标角度的基础值
  let baseTargetX = 0,
    baseTargetY = 0,
    baseTargetZ = 0;
  switch (targetValue) {
    case 1:
      baseTargetX = 0;
      baseTargetY = 0;
      baseTargetZ = 0;
      break;
    case 2:
      baseTargetX = -90;
      baseTargetY = 0;
      baseTargetZ = 0;
      break;
    case 3:
      baseTargetX = 0;
      baseTargetY = -90;
      baseTargetZ = 0;
      break;
    case 4:
      baseTargetX = 0;
      baseTargetY = 90;
      baseTargetZ = 0;
      break;
    case 5:
      baseTargetX = 90;
      baseTargetY = 0;
      baseTargetZ = 0;
      break;
    case 6:
      baseTargetX = 0;
      baseTargetY = 180;
      baseTargetZ = 0;
      break;
  }

  // 添加大量的旋转圈数，确保快速旋转效果
  const extraRotations = 12 + Math.floor(Math.random() * 6); // 12-18圈随机
  const targetX = startRotationX + extraRotations * 360 + baseTargetX;
  const targetY = startRotationY + extraRotations * 360 + baseTargetY;
  const targetZ = startRotationZ + extraRotations * 360 + baseTargetZ;

  // 动画参数
  const totalDuration = 1200; // 1.2秒完成，更快
  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / totalDuration, 1);

    // 使用更强的缓动函数，前期很快，后期快速到位
    const easeProgress =
      progress < 0.7
        ? Math.pow(progress / 0.7, 0.15) * 0.9 // 前70%时间完成90%旋转，非常快
        : 0.9 + Math.pow((progress - 0.7) / 0.3, 2) * 0.1; // 后30%时间快速完成最后10%

    // 插值计算当前角度
    rotationX.value =
      startRotationX + (targetX - startRotationX) * easeProgress;
    rotationY.value =
      startRotationY + (targetY - startRotationY) * easeProgress;
    rotationZ.value =
      startRotationZ + (targetZ - startRotationZ) * easeProgress;

    if (progress < 1) {
      animationId.value = requestAnimFrame(animate);
    } else {
      // 动画结束，精确设置到目标位置并完全停止动画
      setRotationForValue(targetValue);
      isAnimating.value = false;
      // 确保动画完全停止
      if (animationId.value) {
        cancelAnimFrame(animationId.value);
        animationId.value = null;
      }
    }
  };

  isAnimating.value = true;
  animationId.value = requestAnimFrame(animate);
};

// 重新启动连续旋转动画
const restartContinuousAnimation = () => {
  stopAnimation(); // 先停止当前动画

  setTimeout(() => {
    // 重置到随机角度，避免所有骰子同步
    rotationX.value = Math.random() * 360;
    rotationY.value = Math.random() * 360;
    rotationZ.value = Math.random() * 360;

    startContinuousAnimation(); // 重新启动连续动画
  }, 100); // 短暂延迟确保停止完成
};

// 监听 props.value 变化
watch(
  () => props.value,
  (newValue, oldValue) => {
    // 只有在值真正改变时才执行动画
    if (newValue !== oldValue && props.animated) {
      smoothTransitionToValue(newValue);
    } else if (newValue !== oldValue) {
      // 如果不需要动画，直接设置
      setRotationForValue(newValue);
    }
  }
);

// 暴露方法给父组件调用
defineExpose({
  restartContinuousAnimation,
  stopAnimation, // 暴露停止动画方法
});

// 生命周期钩子
onMounted(() => {
  // 先设置初始值，确保骰子可见
  setRotationForValue(props.value);

  // 延迟启动连续旋转动画，给页面加载时间
  setTimeout(() => {
    if (!isAnimating.value) {
      // 只有在没有其他动画时才启动
      // startContinuousAnimation();
    }
  }, 500);
});

onBeforeUnmount(() => {
  stopAnimation();
});
</script>

<template>
  <view
    class="cube-container"
    :style="{
      perspective: `${cubeSize * 6}rpx`,
      '--cube-size': `${cubeSize}rpx`,
    }"
  >
    <!-- Canvas容器，使用Transform来控制立方体模型 -->
    <view
      class="cube-scene"
      :style="{
        ...cubeSceneStyle,
        '--half-cube-size': `${halfCubeSize}rpx`,
        '--dot-size': `${dotSize}rpx`,
      }"
    >
      <!-- 立方体的6个面，直接使用flex布局 -->
      <view
        v-for="(face, index) in faces"
        :key="index"
        :class="`cube-face face-${index + 1} dice-face-${face.dots}`"
        class="face-element"
        :style="faceStyle"
      >
        <!-- 使用flex布局直接放置点数 -->
        <view
          v-for="dotIndex in face.dots"
          :key="dotIndex"
          :class="['dot', face.color === '#d32f2f' ? 'red-dot' : 'blue-dot']"
          :style="{
            width: `${dotSize}rpx`,
            height: `${dotSize}rpx`,
          }"
        ></view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.cube-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--cube-size, 60rpx);
  height: var(--cube-size, 60rpx);
  min-width: var(--cube-size, 60rpx);
  min-height: var(--cube-size, 60rpx);
  /* 确保3D透视正确 */
  perspective-origin: center center;
}

.cube-scene {
  position: relative;
  /* 3D设置 - 保持所有面可见 */
  transform-style: preserve-3d;
  transform-origin: center center center;
  /* 移除过渡，避免与动画冲突 */
  /* transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1); */
}

.face-element {
  position: absolute;
  border-radius: 15%;
  /* 真实象牙白背景 */
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 50%, #f0f0f0 100%);
  /* 简化的圆柱边缘效果 */
  border: 1rpx solid #e0e0e0;
  /* 简化的立体阴影 */
  box-shadow: 0 0 0 1rpx #d0d0d0, 0 2rpx 4rpx rgba(0, 0, 0, 0.12),
    0 1rpx 2rpx rgba(0, 0, 0, 0.08), inset 0 1rpx 1rpx rgba(255, 255, 255, 0.8),
    inset 0 -1rpx 1rpx rgba(0, 0, 0, 0.05);
  /* 基础flex布局 */
  display: flex;
  transition: all 0.3s ease;
  /* 关键：防止透视穿透和修复圆角问题 */
  overflow: hidden;
  box-sizing: border-box;
  backface-visibility: hidden; /* 隐藏背面，避免看到直角白底 */
  opacity: 1;
  /* 确保圆角正确显示 */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

/* 点的基础样式 - 真实骰子的圆锥凹陷效果 */
.dot {
  border-radius: 50%;
  /* 真实骰子点的圆锥凹陷效果 */
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.6), inset 0 -2rpx 3rpx rgba(0, 0, 0, 0.4),
    inset 0 1rpx 2rpx rgba(0, 0, 0, 0.5), inset 0 0 4rpx rgba(0, 0, 0, 0.7),
    inset 0 -1rpx 1rpx rgba(255, 255, 255, 0.1);
  /* 默认蓝色凹陷背景 - 圆锥形渐变 */
  background: radial-gradient(
    circle at center,
    #0d47a1 0%,
    #1565c0 20%,
    #1976d2 40%,
    #1e88e5 60%,
    #2196f3 80%,
    #42a5f5 100%
  );
  transition: all 0.3s ease;
  /* 确保点数完全圆形 */
  aspect-ratio: 1;
}

/* 红色点数样式 - 圆锥形凹陷 */
.dot.red-dot {
  background: radial-gradient(
    circle at center,
    #b71c1c 0%,
    #c62828 20%,
    #d32f2f 40%,
    #e53935 60%,
    #f44336 80%,
    #ef5350 100%
  ) !important;
}

/* 蓝色点数样式 */
.dot.blue-dot {
  background: radial-gradient(
    circle at center,
    #0d47a1 0%,
    #1565c0 20%,
    #1976d2 40%,
    #1e88e5 60%,
    #2196f3 80%,
    #42a5f5 100%
  ) !important;
}

/* 1点 - 居中 */
.dice-face-1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 2点 - 对角线布局（左上到右下） */
.dice-face-2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.dice-face-2 .dot:nth-child(2) {
  align-self: flex-end;
}

/* 3点 - 对角线布局 + 中心点 */
.dice-face-3 {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.dice-face-3 .dot:nth-child(1) {
  align-items: flex-start;
  margin: -3rpx  0 0 3rpx ;
}

.dice-face-3 .dot:nth-child(2) {
  align-self: center;
  
}

.dice-face-3 .dot:nth-child(3) {
  align-self: flex-end;
  margin: 3rpx  0 0 3rpx ;
}

/* 4点 - 两列布局，每列两个点垂直排列 */
.dice-face-4 {
  display: flex;
  justify-content: space-around;
  align-items: stretch;
}

.dice-face-4 .dot {
  position: absolute;
}

.dice-face-4 .dot:nth-child(1) {
  top: calc(25% - var(--dot-size, 8rpx) / 2);
  left: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-4 .dot:nth-child(2) {
  top: calc(25% - var(--dot-size, 8rpx) / 2);
  right: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-4 .dot:nth-child(3) {
  bottom: calc(25% - var(--dot-size, 8rpx) / 2);
  left: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-4 .dot:nth-child(4) {
  bottom: calc(25% - var(--dot-size, 8rpx) / 2);
  right: calc(25% - var(--dot-size, 8rpx) / 2);
}

/* 5点 - 四角 + 中心点 */
.dice-face-5 {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  position: relative;
}

.dice-face-5 .dot:nth-child(1) {
  position: absolute;
  top: calc(25% - var(--dot-size, 8rpx) / 2);
  left: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-5 .dot:nth-child(2) {
  position: absolute;
  top: calc(25% - var(--dot-size, 8rpx) / 2);
  right: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-5 .dot:nth-child(3) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dice-face-5 .dot:nth-child(4) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 8rpx) / 2);
  left: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-5 .dot:nth-child(5) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 8rpx) / 2);
  right: calc(25% - var(--dot-size, 8rpx) / 2);
}

/* 6点 - 三行布局，每行两个点 */
.dice-face-6 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
}

.dice-face-6 .dot:nth-child(1),
.dice-face-6 .dot:nth-child(2) {
  position: absolute;
  top: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-6 .dot:nth-child(3),
.dice-face-6 .dot:nth-child(4) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.dice-face-6 .dot:nth-child(5),
.dice-face-6 .dot:nth-child(6) {
  position: absolute;
  bottom: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-6 .dot:nth-child(1),
.dice-face-6 .dot:nth-child(3),
.dice-face-6 .dot:nth-child(5) {
  left: calc(25% - var(--dot-size, 8rpx) / 2);
}

.dice-face-6 .dot:nth-child(2),
.dice-face-6 .dot:nth-child(4),
.dice-face-6 .dot:nth-child(6) {
  right: calc(25% - var(--dot-size, 8rpx) / 2);
}

/* 立方体面的3D定位 - 真实骰子标准（相对面相加等于7） */
.face-1 {
  transform: translateZ(var(--half-cube-size, 40rpx)); /* 前面 - 1点 */
  z-index: 1;
}

.face-2 {
  transform: rotateY(180deg) translateZ(var(--half-cube-size, 40rpx)); /* 后面 - 6点（1的对面） */
  z-index: 1;
}

.face-3 {
  transform: rotateY(90deg) translateZ(var(--half-cube-size, 40rpx)); /* 右面 - 3点 */
  z-index: 1;
}

.face-4 {
  transform: rotateY(-90deg) translateZ(var(--half-cube-size, 40rpx)); /* 左面 - 4点（3的对面） */
  z-index: 1;
}

.face-5 {
  transform: rotateX(90deg) translateZ(var(--half-cube-size, 40rpx)); /* 上面 - 2点 */
  z-index: 1;
}

.face-6 {
  transform: rotateX(-90deg) translateZ(var(--half-cube-size, 40rpx)); /* 下面 - 5点（2的对面） */
  z-index: 1;
}

.control-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.25) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );

  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2), 0 3rpx 8rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);

  transform: translateY(-2rpx);
}

.control-btn:active {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );

  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

  transform: translateY(1rpx) scale(0.98);
}

.info {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);

  /* 精致背景 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  padding: 25rpx 35rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15);

  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);

  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);

  max-width: 600rpx;
  margin: 0 auto;
}

.info text {
  display: block;
  font-size: 22rpx;
  margin-bottom: 8rpx;
  line-height: 1.4;
  font-weight: 400;
}

.info text:first-child {
  font-size: 26rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 12rpx;
}

.info text:last-child {
  margin-bottom: 0;
}

.face-controls {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.face-btn {
  padding: 12rpx 20rpx;

  /* 精致玻璃质感 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );

  color: rgba(255, 255, 255, 0.85);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 500;

  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);

  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.face-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%
  );

  transform: translateY(-1rpx);
}

.face-btn.active {
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.4) 0%,
    rgba(255, 235, 59, 0.3) 100%
  );

  border-color: rgba(255, 215, 0, 0.6);
  color: #ffd700;

  box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.face-btn:active {
  transform: translateY(1rpx) scale(0.96);
}

.face-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 30rpx;
  justify-content: center;
}

.face-btn {
  padding: 15rpx 25rpx;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.face-btn.active {
  background: rgba(255, 235, 59, 0.3);
  border-color: #ffeb3b;
  color: #ffeb3b;
}

.face-btn:active {
  transform: scale(0.95);
}
</style>
